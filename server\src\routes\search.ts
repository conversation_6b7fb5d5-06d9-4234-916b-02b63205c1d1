import express from 'express';
import { Note } from '../models/Note';
import { Category } from '../models/Category';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { protect } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(protect);

// 全局搜索
router.get('/', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const {
    q: query,
    type = 'all', // all, notes, categories
    page = 1,
    limit = 20,
  } = req.query;

  if (!query || typeof query !== 'string' || query.trim().length === 0) {
    throw new CustomError('搜索关键词不能为空', 400);
  }

  const searchQuery = query.trim();
  const skip = (Number(page) - 1) * Number(limit);

  let results: any = {};

  if (type === 'all' || type === 'notes') {
    // 搜索便签
    const noteQuery = {
      userId,
      $or: [
        { title: { $regex: searchQuery, $options: 'i' } },
        { content: { $regex: searchQuery, $options: 'i' } },
        { tags: { $in: [new RegExp(searchQuery, 'i')] } },
        { category: { $regex: searchQuery, $options: 'i' } },
      ],
    };

    const [notes, notesTotal] = await Promise.all([
      Note.find(noteQuery)
        .sort({ createdAt: -1 })
        .skip(type === 'notes' ? skip : 0)
        .limit(type === 'notes' ? Number(limit) : 10)
        .select('title content category tags color isPinned isArchived createdAt updatedAt')
        .lean(),
      Note.countDocuments(noteQuery),
    ]);

    results.notes = {
      data: notes,
      total: notesTotal,
      ...(type === 'notes' && {
        pagination: {
          page: Number(page),
          limit: Number(limit),
          pages: Math.ceil(notesTotal / Number(limit)),
        },
      }),
    };
  }

  if (type === 'all' || type === 'categories') {
    // 搜索分类
    const categoryQuery = {
      userId,
      name: { $regex: searchQuery, $options: 'i' },
    };

    const [categories, categoriesTotal] = await Promise.all([
      Category.find(categoryQuery)
        .sort({ createdAt: -1 })
        .skip(type === 'categories' ? skip : 0)
        .limit(type === 'categories' ? Number(limit) : 5)
        .populate('notesCount')
        .lean(),
      Category.countDocuments(categoryQuery),
    ]);

    results.categories = {
      data: categories,
      total: categoriesTotal,
      ...(type === 'categories' && {
        pagination: {
          page: Number(page),
          limit: Number(limit),
          pages: Math.ceil(categoriesTotal / Number(limit)),
        },
      }),
    };
  }

  res.json({
    success: true,
    data: {
      query: searchQuery,
      type,
      results,
    },
  });
}));

// 搜索建议
router.get('/suggestions', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { q: query } = req.query;

  if (!query || typeof query !== 'string' || query.trim().length < 2) {
    return res.json({
      success: true,
      data: { suggestions: [] },
    });
  }

  const searchQuery = query.trim();

  // 获取标题建议
  const titleSuggestions = await Note.find({
    userId,
    title: { $regex: searchQuery, $options: 'i' },
  })
    .select('title')
    .limit(5)
    .lean();

  // 获取标签建议
  const tagSuggestions = await Note.aggregate([
    { $match: { userId } },
    { $unwind: '$tags' },
    { $match: { tags: { $regex: searchQuery, $options: 'i' } } },
    { $group: { _id: '$tags', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: 5 },
  ]);

  // 获取分类建议
  const categorySuggestions = await Category.find({
    userId,
    name: { $regex: searchQuery, $options: 'i' },
  })
    .select('name color')
    .limit(3)
    .lean();

  res.json({
    success: true,
    data: {
      suggestions: {
        titles: titleSuggestions.map(note => note.title),
        tags: tagSuggestions.map(tag => tag._id),
        categories: categorySuggestions.map(cat => ({
          name: cat.name,
          color: cat.color,
        })),
      },
    },
  });
}));

// 获取热门标签
router.get('/tags/popular', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { limit = 20 } = req.query;

  const popularTags = await Note.aggregate([
    { $match: { userId, isArchived: false } },
    { $unwind: '$tags' },
    { $group: { _id: '$tags', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: Number(limit) },
  ]);

  res.json({
    success: true,
    data: {
      tags: popularTags.map(tag => ({
        name: tag._id,
        count: tag.count,
      })),
    },
  });
}));

// 获取所有标签
router.get('/tags', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;

  const allTags = await Note.aggregate([
    { $match: { userId } },
    { $unwind: '$tags' },
    { $group: { 
      _id: '$tags', 
      count: { $sum: 1 },
      lastUsed: { $max: '$updatedAt' }
    } },
    { $sort: { lastUsed: -1 } },
  ]);

  res.json({
    success: true,
    data: {
      tags: allTags.map(tag => ({
        name: tag._id,
        count: tag.count,
        lastUsed: tag.lastUsed,
      })),
    },
  });
}));

// 重命名标签
router.put('/tags/:tagName', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { tagName } = req.params;
  const { newName } = req.body;

  if (!newName || typeof newName !== 'string' || newName.trim().length === 0) {
    throw new CustomError('新标签名称不能为空', 400);
  }

  const newTagName = newName.trim();

  if (tagName === newTagName) {
    throw new CustomError('新标签名称与原名称相同', 400);
  }

  // 检查新标签名称是否已存在
  const existingTag = await Note.findOne({
    userId,
    tags: newTagName,
  });

  if (existingTag) {
    throw new CustomError('新标签名称已存在', 400);
  }

  // 更新所有使用此标签的便签
  const result = await Note.updateMany(
    { userId, tags: tagName },
    { $set: { 'tags.$': newTagName } }
  );

  res.json({
    success: true,
    message: '标签重命名成功',
    data: { 
      oldName: tagName,
      newName: newTagName,
      affectedNotes: result.modifiedCount,
    },
  });
}));

// 删除标签
router.delete('/tags/:tagName', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { tagName } = req.params;

  // 从所有便签中移除此标签
  const result = await Note.updateMany(
    { userId, tags: tagName },
    { $pull: { tags: tagName } }
  );

  res.json({
    success: true,
    message: '标签删除成功',
    data: { 
      tagName,
      affectedNotes: result.modifiedCount,
    },
  });
}));

module.exports = router;
