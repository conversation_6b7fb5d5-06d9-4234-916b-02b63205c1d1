#!/bin/bash

# LoftNotes 部署脚本
# 使用方法: ./scripts/deploy.sh [环境] [选项]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
ENVIRONMENT=${1:-development}
BUILD_FRONTEND=${BUILD_FRONTEND:-true}
BUILD_BACKEND=${BUILD_BACKEND:-true}
RUN_TESTS=${RUN_TESTS:-true}
DEPLOY_WITH_DOCKER=${DEPLOY_WITH_DOCKER:-true}

# 显示帮助信息
show_help() {
    echo "LoftNotes 部署脚本"
    echo ""
    echo "使用方法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  development  开发环境 (默认)"
    echo "  staging      测试环境"
    echo "  production   生产环境"
    echo ""
    echo "选项:"
    echo "  --no-frontend    跳过前端构建"
    echo "  --no-backend     跳过后端构建"
    echo "  --no-tests       跳过测试"
    echo "  --no-docker      不使用Docker部署"
    echo "  --help           显示此帮助信息"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-frontend)
            BUILD_FRONTEND=false
            shift
            ;;
        --no-backend)
            BUILD_BACKEND=false
            shift
            ;;
        --no-tests)
            RUN_TESTS=false
            shift
            ;;
        --no-docker)
            DEPLOY_WITH_DOCKER=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        development|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

log_info "开始部署 LoftNotes 到 ${ENVIRONMENT} 环境"

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if [[ "$DEPLOY_WITH_DOCKER" == "true" ]]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker 未安装"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose 未安装"
            exit 1
        fi
    fi
    
    log_success "依赖检查完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    case $ENVIRONMENT in
        development)
            export NODE_ENV=development
            export PORT=5000
            ;;
        staging)
            export NODE_ENV=staging
            export PORT=5000
            ;;
        production)
            export NODE_ENV=production
            export PORT=5000
            ;;
    esac
    
    # 检查必要的环境变量
    if [[ "$ENVIRONMENT" == "production" ]]; then
        if [[ -z "$JWT_SECRET" ]]; then
            log_error "生产环境必须设置 JWT_SECRET"
            exit 1
        fi
        
        if [[ -z "$MONGODB_URI" ]]; then
            log_error "生产环境必须设置 MONGODB_URI"
            exit 1
        fi
    fi
    
    log_success "环境变量设置完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."
    
    # 安装根目录依赖
    npm ci
    
    # 安装前端依赖
    if [[ "$BUILD_FRONTEND" == "true" ]]; then
        log_info "安装前端依赖..."
        cd client && npm ci && cd ..
    fi
    
    # 安装后端依赖
    if [[ "$BUILD_BACKEND" == "true" ]]; then
        log_info "安装后端依赖..."
        cd server && npm ci && cd ..
    fi
    
    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    if [[ "$RUN_TESTS" == "true" ]]; then
        log_info "运行测试..."
        
        # 运行后端测试
        if [[ "$BUILD_BACKEND" == "true" ]]; then
            log_info "运行后端测试..."
            cd server && npm test && cd ..
        fi
        
        # 运行前端测试
        if [[ "$BUILD_FRONTEND" == "true" ]]; then
            log_info "运行前端测试..."
            cd client && npm test -- --watchAll=false && cd ..
        fi
        
        log_success "测试完成"
    else
        log_warning "跳过测试"
    fi
}

# 构建应用
build_application() {
    log_info "构建应用..."
    
    # 构建前端
    if [[ "$BUILD_FRONTEND" == "true" ]]; then
        log_info "构建前端..."
        cd client && npm run build && cd ..
        log_success "前端构建完成"
    fi
    
    # 构建后端
    if [[ "$BUILD_BACKEND" == "true" ]]; then
        log_info "构建后端..."
        cd server && npm run build && cd ..
        log_success "后端构建完成"
    fi
    
    log_success "应用构建完成"
}

# Docker部署
deploy_with_docker() {
    log_info "使用 Docker 部署..."
    
    # 停止现有容器
    docker-compose down
    
    # 构建镜像
    docker-compose build
    
    # 启动服务
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker-compose up -d
    else
        docker-compose up -d --scale prometheus=0 --scale grafana=0
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 健康检查
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        docker-compose logs app
        exit 1
    fi
    
    log_success "Docker 部署完成"
}

# 传统部署
deploy_traditional() {
    log_info "使用传统方式部署..."
    
    # 停止现有进程
    if pgrep -f "node.*dist/index.js" > /dev/null; then
        log_info "停止现有进程..."
        pkill -f "node.*dist/index.js"
        sleep 2
    fi
    
    # 启动应用
    log_info "启动应用..."
    cd server
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        nohup node dist/index.js > ../logs/app.log 2>&1 &
    else
        node dist/index.js &
    fi
    
    cd ..
    
    # 等待服务启动
    sleep 5
    
    # 健康检查
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
    
    log_success "传统部署完成"
}

# 部署后检查
post_deploy_check() {
    log_info "执行部署后检查..."
    
    # 检查服务状态
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        exit 1
    fi
    
    # 检查数据库连接
    log_info "检查数据库连接..."
    # 这里可以添加数据库连接检查逻辑
    
    log_success "部署后检查完成"
}

# 主执行流程
main() {
    log_info "开始部署流程..."
    
    check_dependencies
    setup_environment
    install_dependencies
    run_tests
    build_application
    
    if [[ "$DEPLOY_WITH_DOCKER" == "true" ]]; then
        deploy_with_docker
    else
        deploy_traditional
    fi
    
    post_deploy_check
    
    log_success "🎉 LoftNotes 部署完成!"
    log_info "应用访问地址: http://localhost:5000"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "监控面板: http://localhost:3001 (如果启用)"
    fi
}

# 创建必要的目录
mkdir -p logs uploads

# 执行主流程
main
