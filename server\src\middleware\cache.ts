import { Request, Response, NextFunction } from 'express';
import { redisConfig } from '../config/redis';
import { AuthRequest } from './auth';

interface CacheOptions {
  ttl?: number; // 缓存时间（秒）
  keyGenerator?: (req: Request) => string; // 自定义键生成器
  condition?: (req: Request, res: Response) => boolean; // 缓存条件
  skipCache?: (req: Request) => boolean; // 跳过缓存条件
}

/**
 * 缓存中间件
 */
export const cache = (options: CacheOptions = {}) => {
  const {
    ttl = 300, // 默认5分钟
    keyGenerator,
    condition,
    skipCache,
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // 检查是否跳过缓存
    if (skipCache && skipCache(req)) {
      return next();
    }

    // 只缓存 GET 请求
    if (req.method !== 'GET') {
      return next();
    }

    // 生成缓存键
    const cacheKey = keyGenerator 
      ? keyGenerator(req)
      : generateDefaultCacheKey(req);

    try {
      // 尝试从缓存获取数据
      const cachedData = await redisConfig.get(cacheKey);
      
      if (cachedData) {
        console.log(`🎯 缓存命中: ${cacheKey}`);
        return res.json(cachedData);
      }

      // 缓存未命中，继续执行
      console.log(`❌ 缓存未命中: ${cacheKey}`);

      // 拦截响应
      const originalJson = res.json;
      res.json = function(data: any) {
        // 检查缓存条件
        if (!condition || condition(req, res)) {
          // 异步缓存数据，不阻塞响应
          setImmediate(async () => {
            try {
              await redisConfig.set(cacheKey, data, ttl);
              console.log(`💾 数据已缓存: ${cacheKey}`);
            } catch (error) {
              console.error('❌ 缓存设置失败:', error);
            }
          });
        }

        // 调用原始的 json 方法
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('❌ 缓存中间件错误:', error);
      next();
    }
  };
};

/**
 * 生成默认缓存键
 */
function generateDefaultCacheKey(req: Request): string {
  const authReq = req as AuthRequest;
  const userId = authReq.user?._id || 'anonymous';
  const path = req.path;
  const query = JSON.stringify(req.query);
  
  return `cache:${userId}:${path}:${Buffer.from(query).toString('base64')}`;
}

/**
 * 用户相关缓存键生成器
 */
export const userCacheKey = (req: Request): string => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?._id || 'anonymous';
  const path = req.path;
  const query = JSON.stringify(req.query);
  
  return `user:${userId}:${path}:${Buffer.from(query).toString('base64')}`;
};

/**
 * 便签缓存键生成器
 */
export const notesCacheKey = (req: Request): string => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?._id;
  const query = JSON.stringify(req.query);
  
  return `notes:${userId}:${Buffer.from(query).toString('base64')}`;
};

/**
 * 分类缓存键生成器
 */
export const categoriesCacheKey = (req: Request): string => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?._id;
  
  return `categories:${userId}`;
};

/**
 * 清除用户相关缓存
 */
export const clearUserCache = async (userId: string): Promise<void> => {
  try {
    const patterns = [
      `user:${userId}:*`,
      `notes:${userId}:*`,
      `categories:${userId}`,
      `cache:${userId}:*`,
    ];

    for (const pattern of patterns) {
      const deletedCount = await redisConfig.deletePattern(pattern);
      if (deletedCount > 0) {
        console.log(`🗑️ 清除缓存: ${pattern} (${deletedCount} 个键)`);
      }
    }
  } catch (error) {
    console.error('❌ 清除用户缓存失败:', error);
  }
};

/**
 * 清除便签相关缓存
 */
export const clearNotesCache = async (userId: string): Promise<void> => {
  try {
    const patterns = [
      `notes:${userId}:*`,
      `cache:${userId}:/api/notes*`,
    ];

    for (const pattern of patterns) {
      const deletedCount = await redisConfig.deletePattern(pattern);
      if (deletedCount > 0) {
        console.log(`🗑️ 清除便签缓存: ${pattern} (${deletedCount} 个键)`);
      }
    }
  } catch (error) {
    console.error('❌ 清除便签缓存失败:', error);
  }
};

/**
 * 清除分类相关缓存
 */
export const clearCategoriesCache = async (userId: string): Promise<void> => {
  try {
    const patterns = [
      `categories:${userId}`,
      `cache:${userId}:/api/categories*`,
    ];

    for (const pattern of patterns) {
      const deletedCount = await redisConfig.deletePattern(pattern);
      if (deletedCount > 0) {
        console.log(`🗑️ 清除分类缓存: ${pattern} (${deletedCount} 个键)`);
      }
    }
  } catch (error) {
    console.error('❌ 清除分类缓存失败:', error);
  }
};

/**
 * 缓存预热
 */
export const warmupCache = async (userId: string): Promise<void> => {
  try {
    console.log(`🔥 开始缓存预热: ${userId}`);
    
    // 这里可以预加载用户常用的数据
    // 例如：最近的便签、常用分类等
    
    console.log(`✅ 缓存预热完成: ${userId}`);
  } catch (error) {
    console.error('❌ 缓存预热失败:', error);
  }
};
