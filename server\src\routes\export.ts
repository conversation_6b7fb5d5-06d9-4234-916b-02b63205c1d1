import express from 'express';
import { Note } from '../models/Note';
import { Category } from '../models/Category';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { protect } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(protect);

// 导出便签数据
router.get('/notes', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { format = 'json', includeArchived = 'false' } = req.query;

  // 构建查询条件
  const query: any = { userId };
  if (includeArchived === 'false') {
    query.isArchived = false;
  }

  // 获取便签数据
  const notes = await Note.find(query)
    .sort({ createdAt: -1 })
    .lean();

  // 获取分类数据
  const categories = await Category.find({ userId })
    .lean();

  const exportData = {
    exportDate: new Date().toISOString(),
    totalNotes: notes.length,
    totalCategories: categories.length,
    notes: notes.map(note => ({
      id: note._id,
      title: note.title,
      content: note.content,
      category: note.category,
      tags: note.tags,
      color: note.color,
      isPinned: note.isPinned,
      isArchived: note.isArchived,
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
    })),
    categories: categories.map(cat => ({
      id: cat._id,
      name: cat.name,
      color: cat.color,
      createdAt: cat.createdAt,
      updatedAt: cat.updatedAt,
    })),
  };

  if (format === 'json') {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename="loftnotes-export.json"');
    res.json(exportData);
  } else if (format === 'csv') {
    // CSV格式导出
    const csvHeader = 'ID,标题,内容,分类,标签,颜色,置顶,归档,创建时间,更新时间\n';
    const csvRows = notes.map(note => {
      const escapeCsv = (str: string) => `"${str.replace(/"/g, '""')}"`;
      return [
        note._id,
        escapeCsv(note.title),
        escapeCsv(note.content.substring(0, 100) + (note.content.length > 100 ? '...' : '')),
        escapeCsv(note.category || ''),
        escapeCsv(note.tags.join(', ')),
        note.color,
        note.isPinned ? '是' : '否',
        note.isArchived ? '是' : '否',
        note.createdAt.toISOString(),
        note.updatedAt.toISOString(),
      ].join(',');
    }).join('\n');

    const csvContent = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename="loftnotes-export.csv"');
    res.send('\ufeff' + csvContent); // 添加BOM以支持中文
  } else if (format === 'markdown') {
    // Markdown格式导出
    let markdownContent = '# LoftNotes 导出\n\n';
    markdownContent += `导出时间: ${new Date().toLocaleString('zh-CN')}\n`;
    markdownContent += `便签总数: ${notes.length}\n`;
    markdownContent += `分类总数: ${categories.length}\n\n`;

    // 按分类组织便签
    const notesByCategory: { [key: string]: any[] } = {};
    notes.forEach(note => {
      const category = note.category || '未分类';
      if (!notesByCategory[category]) {
        notesByCategory[category] = [];
      }
      notesByCategory[category].push(note);
    });

    Object.entries(notesByCategory).forEach(([category, categoryNotes]) => {
      markdownContent += `## ${category}\n\n`;
      categoryNotes.forEach(note => {
        markdownContent += `### ${note.title}\n\n`;
        markdownContent += `${note.content}\n\n`;
        if (note.tags.length > 0) {
          markdownContent += `**标签:** ${note.tags.map((tag: string) => `\`${tag}\``).join(', ')}\n\n`;
        }
        markdownContent += `**创建时间:** ${note.createdAt.toLocaleString('zh-CN')}\n\n`;
        markdownContent += '---\n\n';
      });
    });

    res.setHeader('Content-Type', 'text/markdown; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename="loftnotes-export.md"');
    res.send(markdownContent);
  } else {
    throw new CustomError('不支持的导出格式', 400);
  }
}));

// 导入便签数据
router.post('/notes', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { data, options = {} } = req.body;

  if (!data || !Array.isArray(data.notes)) {
    throw new CustomError('导入数据格式错误', 400);
  }

  const {
    skipDuplicates = true,
    updateExisting = false,
    importCategories = true,
  } = options;

  let importedNotes = 0;
  let importedCategories = 0;
  let skippedNotes = 0;
  let errors: string[] = [];

  // 导入分类
  if (importCategories && data.categories && Array.isArray(data.categories)) {
    for (const categoryData of data.categories) {
      try {
        const existingCategory = await Category.findOne({
          userId,
          name: categoryData.name,
        });

        if (!existingCategory) {
          await Category.create({
            name: categoryData.name,
            color: categoryData.color || '#3B82F6',
            userId,
          });
          importedCategories++;
        }
      } catch (error) {
        errors.push(`分类 "${categoryData.name}" 导入失败: ${(error as Error).message}`);
      }
    }
  }

  // 导入便签
  for (const noteData of data.notes) {
    try {
      // 检查是否存在相同标题的便签
      const existingNote = await Note.findOne({
        userId,
        title: noteData.title,
      });

      if (existingNote) {
        if (skipDuplicates && !updateExisting) {
          skippedNotes++;
          continue;
        } else if (updateExisting) {
          await Note.findByIdAndUpdate(existingNote._id, {
            content: noteData.content,
            category: noteData.category,
            tags: noteData.tags || [],
            color: noteData.color || '#ffffff',
            isPinned: noteData.isPinned || false,
          });
          importedNotes++;
          continue;
        }
      }

      // 创建新便签
      await Note.create({
        title: noteData.title,
        content: noteData.content,
        category: noteData.category,
        tags: noteData.tags || [],
        color: noteData.color || '#ffffff',
        isPinned: noteData.isPinned || false,
        isArchived: noteData.isArchived || false,
        userId,
      });
      importedNotes++;
    } catch (error) {
      errors.push(`便签 "${noteData.title}" 导入失败: ${(error as Error).message}`);
    }
  }

  res.json({
    success: true,
    message: '数据导入完成',
    data: {
      importedNotes,
      importedCategories,
      skippedNotes,
      errors,
      summary: {
        total: data.notes.length,
        successful: importedNotes,
        failed: errors.length,
        skipped: skippedNotes,
      },
    },
  });
}));

// 获取导入模板
router.get('/template', asyncHandler(async (req, res) => {
  const template = {
    exportDate: new Date().toISOString(),
    totalNotes: 2,
    totalCategories: 1,
    notes: [
      {
        title: '示例便签1',
        content: '这是一个示例便签的内容。您可以在这里写下任何想要记录的信息。',
        category: '工作',
        tags: ['示例', '模板'],
        color: '#ffffff',
        isPinned: false,
        isArchived: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        title: '示例便签2',
        content: '这是另一个示例便签。支持Markdown格式和富文本内容。',
        category: '个人',
        tags: ['示例', '个人'],
        color: '#fef3c7',
        isPinned: true,
        isArchived: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ],
    categories: [
      {
        name: '工作',
        color: '#3B82F6',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        name: '个人',
        color: '#10B981',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ],
  };

  res.json({
    success: true,
    message: '导入模板获取成功',
    data: template,
  });
}));

module.exports = router;
