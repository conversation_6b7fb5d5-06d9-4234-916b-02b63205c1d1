// 简单的认证系统测试脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

class AuthTester {
  constructor() {
    this.token = null;
    this.refreshToken = null;
  }

  async testRegister() {
    console.log('🧪 测试用户注册...');
    try {
      const response = await axios.post(`${BASE_URL}/auth/register`, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      });

      if (response.data.success) {
        this.token = response.data.data.token;
        this.refreshToken = response.data.data.refreshToken;
        console.log('✅ 用户注册成功');
        return true;
      }
    } catch (error) {
      console.log('❌ 用户注册失败:', error.response?.data?.error || error.message);
      return false;
    }
  }

  async testLogin() {
    console.log('🧪 测试用户登录...');
    try {
      const response = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      if (response.data.success) {
        this.token = response.data.data.token;
        this.refreshToken = response.data.data.refreshToken;
        console.log('✅ 用户登录成功');
        return true;
      }
    } catch (error) {
      console.log('❌ 用户登录失败:', error.response?.data?.error || error.message);
      return false;
    }
  }

  async testGetProfile() {
    console.log('🧪 测试获取用户信息...');
    try {
      const response = await axios.get(`${BASE_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${this.token}`
        }
      });

      if (response.data.success) {
        console.log('✅ 获取用户信息成功');
        console.log('👤 用户信息:', {
          username: response.data.data.user.username,
          email: response.data.data.user.email,
          role: response.data.data.user.role
        });
        return true;
      }
    } catch (error) {
      console.log('❌ 获取用户信息失败:', error.response?.data?.error || error.message);
      return false;
    }
  }

  async testRefreshToken() {
    console.log('🧪 测试令牌刷新...');
    try {
      const response = await axios.post(`${BASE_URL}/auth/refresh`, {
        refreshToken: this.refreshToken
      });

      if (response.data.success) {
        this.token = response.data.data.accessToken;
        this.refreshToken = response.data.data.refreshToken;
        console.log('✅ 令牌刷新成功');
        return true;
      }
    } catch (error) {
      console.log('❌ 令牌刷新失败:', error.response?.data?.error || error.message);
      return false;
    }
  }

  async runAllTests() {
    console.log('🚀 开始认证系统测试...\n');

    const results = {
      register: await this.testRegister(),
      login: await this.testLogin(),
      profile: await this.testGetProfile(),
      refresh: await this.testRefreshToken(),
    };

    console.log('\n📊 测试结果汇总:');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`);
    });

    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n🎯 总体结果: ${passedCount}/${totalCount} 测试通过`);
    
    if (passedCount === totalCount) {
      console.log('🎉 所有认证功能测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查服务器状态');
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const tester = new AuthTester();
  tester.runAllTests().catch(console.error);
}

module.exports = AuthTester;
