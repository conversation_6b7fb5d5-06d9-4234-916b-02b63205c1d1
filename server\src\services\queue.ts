import Queue from 'bull';
import { redisConfig } from '../config/redis';

// 队列配置
const queueConfig = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined,
  },
  defaultJobOptions: {
    removeOnComplete: 10, // 保留最近10个完成的任务
    removeOnFail: 50, // 保留最近50个失败的任务
    attempts: 3, // 重试次数
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// 邮件队列
export const emailQueue = new Queue('email processing', queueConfig);

// 数据备份队列
export const backupQueue = new Queue('data backup', queueConfig);

// 通知队列
export const notificationQueue = new Queue('notification processing', queueConfig);

// 数据清理队列
export const cleanupQueue = new Queue('data cleanup', queueConfig);

// 邮件任务处理器
emailQueue.process('send-welcome-email', async (job) => {
  const { userEmail, username } = job.data;
  
  console.log(`📧 发送欢迎邮件给: ${userEmail}`);
  
  try {
    // 这里集成实际的邮件服务（如 SendGrid, Nodemailer 等）
    await sendWelcomeEmail(userEmail, username);
    
    console.log(`✅ 欢迎邮件发送成功: ${userEmail}`);
    return { success: true, email: userEmail };
  } catch (error) {
    console.error(`❌ 欢迎邮件发送失败: ${userEmail}`, error);
    throw error;
  }
});

emailQueue.process('send-notification-email', async (job) => {
  const { userEmail, subject, content } = job.data;
  
  console.log(`📧 发送通知邮件给: ${userEmail}`);
  
  try {
    await sendNotificationEmail(userEmail, subject, content);
    
    console.log(`✅ 通知邮件发送成功: ${userEmail}`);
    return { success: true, email: userEmail };
  } catch (error) {
    console.error(`❌ 通知邮件发送失败: ${userEmail}`, error);
    throw error;
  }
});

// 数据备份任务处理器
backupQueue.process('backup-user-data', async (job) => {
  const { userId } = job.data;
  
  console.log(`💾 开始备份用户数据: ${userId}`);
  
  try {
    await backupUserData(userId);
    
    console.log(`✅ 用户数据备份完成: ${userId}`);
    return { success: true, userId };
  } catch (error) {
    console.error(`❌ 用户数据备份失败: ${userId}`, error);
    throw error;
  }
});

// 通知任务处理器
notificationQueue.process('send-push-notification', async (job) => {
  const { userId, title, message, data } = job.data;
  
  console.log(`🔔 发送推送通知给用户: ${userId}`);
  
  try {
    await sendPushNotification(userId, title, message, data);
    
    console.log(`✅ 推送通知发送成功: ${userId}`);
    return { success: true, userId };
  } catch (error) {
    console.error(`❌ 推送通知发送失败: ${userId}`, error);
    throw error;
  }
});

// 数据清理任务处理器
cleanupQueue.process('cleanup-expired-data', async (job) => {
  console.log('🧹 开始清理过期数据');
  
  try {
    const result = await cleanupExpiredData();
    
    console.log('✅ 过期数据清理完成', result);
    return result;
  } catch (error) {
    console.error('❌ 过期数据清理失败', error);
    throw error;
  }
});

// 队列任务添加函数
export const addEmailJob = async (type: string, data: any, options?: any) => {
  try {
    const job = await emailQueue.add(type, data, {
      ...queueConfig.defaultJobOptions,
      ...options,
    });
    console.log(`📧 邮件任务已添加: ${job.id}`);
    return job;
  } catch (error) {
    console.error('❌ 添加邮件任务失败:', error);
    throw error;
  }
};

export const addBackupJob = async (type: string, data: any, options?: any) => {
  try {
    const job = await backupQueue.add(type, data, {
      ...queueConfig.defaultJobOptions,
      ...options,
    });
    console.log(`💾 备份任务已添加: ${job.id}`);
    return job;
  } catch (error) {
    console.error('❌ 添加备份任务失败:', error);
    throw error;
  }
};

export const addNotificationJob = async (type: string, data: any, options?: any) => {
  try {
    const job = await notificationQueue.add(type, data, {
      ...queueConfig.defaultJobOptions,
      ...options,
    });
    console.log(`🔔 通知任务已添加: ${job.id}`);
    return job;
  } catch (error) {
    console.error('❌ 添加通知任务失败:', error);
    throw error;
  }
};

// 定时任务
export const setupScheduledJobs = () => {
  // 每天凌晨2点清理过期数据
  cleanupQueue.add('cleanup-expired-data', {}, {
    repeat: { cron: '0 2 * * *' },
    removeOnComplete: 5,
    removeOnFail: 5,
  });

  console.log('⏰ 定时任务已设置');
};

// 实际的邮件发送函数（示例）
async function sendWelcomeEmail(email: string, username: string): Promise<void> {
  // 这里集成实际的邮件服务
  console.log(`发送欢迎邮件给 ${username} (${email})`);
  
  // 模拟邮件发送延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
}

async function sendNotificationEmail(email: string, subject: string, content: string): Promise<void> {
  // 这里集成实际的邮件服务
  console.log(`发送通知邮件: ${subject} 给 ${email}`);
  
  // 模拟邮件发送延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
}

// 实际的数据备份函数（示例）
async function backupUserData(userId: string): Promise<void> {
  // 这里实现实际的数据备份逻辑
  console.log(`备份用户 ${userId} 的数据`);
  
  // 模拟备份延迟
  await new Promise(resolve => setTimeout(resolve, 2000));
}

// 实际的推送通知函数（示例）
async function sendPushNotification(userId: string, title: string, message: string, data?: any): Promise<void> {
  // 这里集成实际的推送服务
  console.log(`发送推送通知给用户 ${userId}: ${title}`);
  
  // 模拟推送延迟
  await new Promise(resolve => setTimeout(resolve, 500));
}

// 实际的数据清理函数（示例）
async function cleanupExpiredData(): Promise<any> {
  // 这里实现实际的数据清理逻辑
  console.log('清理过期的缓存和临时数据');
  
  // 清理过期的Redis缓存
  const deletedKeys = await redisConfig.deletePattern('temp:*');
  
  return {
    deletedCacheKeys: deletedKeys,
    timestamp: new Date().toISOString(),
  };
}

// 队列监控
export const setupQueueMonitoring = () => {
  const queues = [emailQueue, backupQueue, notificationQueue, cleanupQueue];
  
  queues.forEach(queue => {
    queue.on('completed', (job) => {
      console.log(`✅ 任务完成: ${queue.name} - ${job.id}`);
    });
    
    queue.on('failed', (job, err) => {
      console.error(`❌ 任务失败: ${queue.name} - ${job.id}`, err.message);
    });
    
    queue.on('stalled', (job) => {
      console.warn(`⚠️ 任务停滞: ${queue.name} - ${job.id}`);
    });
  });
  
  console.log('📊 队列监控已启动');
};
