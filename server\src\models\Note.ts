import mongoose, { Document, Schema } from 'mongoose';

export interface INote extends Document {
  title: string;
  content: string;
  category?: string;
  tags: string[];
  color: string;
  isPinned: boolean;
  isArchived: boolean;
  userId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const noteSchema = new Schema<INote>({
  title: {
    type: String,
    required: [true, '便签标题不能为空'],
    trim: true,
    maxlength: [200, '标题不能超过200个字符'],
  },
  content: {
    type: String,
    required: [true, '便签内容不能为空'],
    maxlength: [50000, '内容不能超过50000个字符'],
  },
  category: {
    type: String,
    trim: true,
    maxlength: [50, '分类名称不能超过50个字符'],
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签不能超过30个字符'],
  }],
  color: {
    type: String,
    default: '#ffffff',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, '请输入有效的颜色值'],
  },
  isPinned: {
    type: Boolean,
    default: false,
  },
  isArchived: {
    type: Boolean,
    default: false,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true,
});

// 索引
noteSchema.index({ userId: 1, createdAt: -1 });
noteSchema.index({ userId: 1, isPinned: -1, createdAt: -1 });
noteSchema.index({ userId: 1, isArchived: 1 });
noteSchema.index({ userId: 1, category: 1 });
noteSchema.index({ userId: 1, tags: 1 });
noteSchema.index({ title: 'text', content: 'text' }); // 全文搜索索引

// 验证标签数量
noteSchema.pre('save', function(next) {
  if (this.tags && this.tags.length > 10) {
    next(new Error('标签数量不能超过10个'));
  } else {
    next();
  }
});

// 虚拟字段：便签摘要
noteSchema.virtual('summary').get(function() {
  if (this.content.length <= 100) {
    return this.content;
  }
  return this.content.substring(0, 100) + '...';
});

// 确保虚拟字段被序列化
noteSchema.set('toJSON', { virtuals: true });
noteSchema.set('toObject', { virtuals: true });

export const Note = mongoose.model<INote>('Note', noteSchema);
