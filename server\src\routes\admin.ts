import express from 'express';
import { User } from '../models/User';
import { Note } from '../models/Note';
import { Category } from '../models/Category';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// 所有管理员路由都需要认证和管理员权限
router.use(protect);
router.use(authorize('admin'));

// 获取系统统计信息
router.get('/stats', asyncHandler(async (req, res) => {
  const now = new Date();
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

  const [
    totalUsers,
    activeUsers,
    totalNotes,
    totalCategories,
    newUsersThisMonth,
    newUsersLastMonth,
    notesCreatedThis<PERSON>onth,
    notesCreatedLastMonth,
  ] = await Promise.all([
    User.countDocuments(),
    User.countDocuments({ isActive: true }),
    Note.countDocuments(),
    Category.countDocuments(),
    User.countDocuments({ createdAt: { $gte: thisMonth } }),
    User.countDocuments({ 
      createdAt: { $gte: lastMonth, $lt: thisMonth } 
    }),
    Note.countDocuments({ createdAt: { $gte: thisMonth } }),
    Note.countDocuments({ 
      createdAt: { $gte: lastMonth, $lt: thisMonth } 
    }),
  ]);

  // 计算增长率
  const userGrowthRate = newUsersLastMonth > 0 
    ? ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth * 100).toFixed(1)
    : '0';

  const noteGrowthRate = notesCreatedLastMonth > 0
    ? ((notesCreatedThisMonth - notesCreatedLastMonth) / notesCreatedLastMonth * 100).toFixed(1)
    : '0';

  res.json({
    success: true,
    data: {
      totalUsers,
      activeUsers,
      totalNotes,
      totalCategories,
      newUsersThisMonth,
      notesCreatedThisMonth,
      userGrowthRate: `${userGrowthRate}%`,
      noteGrowthRate: `${noteGrowthRate}%`,
    },
  });
}));

// 获取用户列表
router.get('/users', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    search,
    role,
    isActive,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = req.query;

  // 构建查询条件
  const query: any = {};

  if (search) {
    query.$or = [
      { username: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
    ];
  }

  if (role) {
    query.role = role;
  }

  if (isActive !== undefined) {
    query.isActive = isActive === 'true';
  }

  // 排序
  const sortOptions: any = {};
  sortOptions[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

  // 分页
  const skip = (Number(page) - 1) * Number(limit);

  // 执行查询
  const [users, total] = await Promise.all([
    User.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(Number(limit))
      .populate('notesCount')
      .lean(),
    User.countDocuments(query),
  ]);

  res.json({
    success: true,
    data: {
      users,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// 获取单个用户详情
router.get('/users/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findById(id).populate('notesCount');

  if (!user) {
    throw new CustomError('用户不存在', 404);
  }

  // 获取用户的便签统计
  const [totalNotes, pinnedNotes, archivedNotes, categories] = await Promise.all([
    Note.countDocuments({ userId: id }),
    Note.countDocuments({ userId: id, isPinned: true }),
    Note.countDocuments({ userId: id, isArchived: true }),
    Note.distinct('category', { userId: id, category: { $ne: null } }),
  ]);

  res.json({
    success: true,
    data: {
      user,
      stats: {
        totalNotes,
        pinnedNotes,
        archivedNotes,
        categoriesCount: categories.length,
      },
    },
  });
}));

// 更新用户状态
router.patch('/users/:id/status', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  if (typeof isActive !== 'boolean') {
    throw new CustomError('isActive必须是布尔值', 400);
  }

  const user = await User.findByIdAndUpdate(
    id,
    { isActive },
    { new: true }
  );

  if (!user) {
    throw new CustomError('用户不存在', 404);
  }

  res.json({
    success: true,
    message: `用户已${isActive ? '启用' : '禁用'}`,
    data: { user },
  });
}));

// 更新用户角色
router.patch('/users/:id/role', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { role } = req.body;

  if (!['user', 'admin'].includes(role)) {
    throw new CustomError('角色只能是user或admin', 400);
  }

  // 防止管理员修改自己的角色
  if (id === (req as any).user._id.toString()) {
    throw new CustomError('不能修改自己的角色', 400);
  }

  const user = await User.findByIdAndUpdate(
    id,
    { role },
    { new: true }
  );

  if (!user) {
    throw new CustomError('用户不存在', 404);
  }

  res.json({
    success: true,
    message: '用户角色更新成功',
    data: { user },
  });
}));

// 删除用户
router.delete('/users/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 防止管理员删除自己
  if (id === (req as any).user._id.toString()) {
    throw new CustomError('不能删除自己的账户', 400);
  }

  const user = await User.findById(id);

  if (!user) {
    throw new CustomError('用户不存在', 404);
  }

  // 删除用户的所有便签和分类
  await Promise.all([
    Note.deleteMany({ userId: id }),
    Category.deleteMany({ userId: id }),
    User.findByIdAndDelete(id),
  ]);

  res.json({
    success: true,
    message: '用户及其所有数据已删除',
  });
}));

// 获取系统便签列表
router.get('/notes', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    search,
    userId,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = req.query;

  // 构建查询条件
  const query: any = {};

  if (search) {
    query.$or = [
      { title: { $regex: search, $options: 'i' } },
      { content: { $regex: search, $options: 'i' } },
    ];
  }

  if (userId) {
    query.userId = userId;
  }

  // 排序
  const sortOptions: any = {};
  sortOptions[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

  // 分页
  const skip = (Number(page) - 1) * Number(limit);

  // 执行查询
  const [notes, total] = await Promise.all([
    Note.find(query)
      .populate('userId', 'username email')
      .sort(sortOptions)
      .skip(skip)
      .limit(Number(limit))
      .lean(),
    Note.countDocuments(query),
  ]);

  res.json({
    success: true,
    data: {
      notes,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// 删除便签
router.delete('/notes/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const note = await Note.findByIdAndDelete(id);

  if (!note) {
    throw new CustomError('便签不存在', 404);
  }

  res.json({
    success: true,
    message: '便签删除成功',
  });
}));

// 获取系统配置
router.get('/config', asyncHandler(async (req, res) => {
  // 这里可以从数据库或配置文件读取系统配置
  const config = {
    siteName: process.env.SITE_NAME || 'LoftNotes',
    siteDescription: process.env.SITE_DESCRIPTION || '现代化电子便签系统',
    allowRegistration: process.env.ALLOW_REGISTRATION !== 'false',
    maxNotesPerUser: parseInt(process.env.MAX_NOTES_PER_USER || '1000'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'),
    supportedFileTypes: (process.env.SUPPORTED_FILE_TYPES || 'jpg,jpeg,png,gif,pdf,txt,md').split(','),
  };

  res.json({
    success: true,
    data: { config },
  });
}));

module.exports = router;
