{"name": "loft-notes-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "axios": "^1.6.2", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-textarea": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-collapsible": "^1.0.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}