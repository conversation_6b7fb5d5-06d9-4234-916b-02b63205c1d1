// MongoDB 初始化脚本
// 这个脚本会在 MongoDB 容器首次启动时执行

// 切换到应用数据库
db = db.getSiblingDB('loftnotes');

// 创建应用用户
db.createUser({
  user: 'loftnotes_user',
  pwd: 'loftnotes_password',
  roles: [
    {
      role: 'readWrite',
      db: 'loftnotes'
    }
  ]
});

// 创建索引以提高性能
print('创建数据库索引...');

// 用户集合索引
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 }, { unique: true });
db.users.createIndex({ createdAt: -1 });
db.users.createIndex({ isActive: 1 });

// 便签集合索引
db.notes.createIndex({ userId: 1, createdAt: -1 });
db.notes.createIndex({ userId: 1, isPinned: -1, createdAt: -1 });
db.notes.createIndex({ userId: 1, isArchived: 1 });
db.notes.createIndex({ userId: 1, category: 1 });
db.notes.createIndex({ userId: 1, tags: 1 });
db.notes.createIndex({ title: 'text', content: 'text' });

// 分类集合索引
db.categories.createIndex({ userId: 1, name: 1 }, { unique: true });
db.categories.createIndex({ userId: 1, createdAt: -1 });

// 创建演示数据（仅在开发环境）
if (process.env.NODE_ENV !== 'production') {
  print('创建演示数据...');
  
  // 创建演示用户
  const demoUserId = ObjectId();
  db.users.insertOne({
    _id: demoUserId,
    username: 'demo',
    email: '<EMAIL>',
    password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIfy', // demo123456
    role: 'user',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  });
  
  // 创建管理员用户
  const adminUserId = ObjectId();
  db.users.insertOne({
    _id: adminUserId,
    username: 'admin',
    email: '<EMAIL>',
    password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIfy', // admin123456
    role: 'admin',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  });
  
  // 创建演示分类
  const workCategoryId = ObjectId();
  const personalCategoryId = ObjectId();
  
  db.categories.insertMany([
    {
      _id: workCategoryId,
      name: '工作',
      color: '#3B82F6',
      userId: demoUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: personalCategoryId,
      name: '个人',
      color: '#10B981',
      userId: demoUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);
  
  // 创建演示便签
  db.notes.insertMany([
    {
      _id: ObjectId(),
      title: '欢迎使用 LoftNotes',
      content: '这是您的第一个便签！\n\nLoftNotes 是一个现代化的电子便签系统，具有以下特性：\n\n- 📝 创建和编辑便签\n- 🏷️ 分类和标签管理\n- 🔍 全文搜索功能\n- 📱 响应式设计\n- 🔄 实时同步\n\n开始创建您的便签吧！',
      category: '个人',
      tags: ['欢迎', '介绍'],
      color: '#fef3c7',
      isPinned: true,
      isArchived: false,
      userId: demoUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: ObjectId(),
      title: '项目待办事项',
      content: '## 本周任务\n\n- [x] 完成用户认证模块\n- [x] 实现便签CRUD功能\n- [ ] 添加搜索功能\n- [ ] 优化界面设计\n- [ ] 编写单元测试\n\n## 下周计划\n\n- [ ] 部署到生产环境\n- [ ] 性能优化\n- [ ] 用户反馈收集',
      category: '工作',
      tags: ['待办', '项目', '计划'],
      color: '#dbeafe',
      isPinned: false,
      isArchived: false,
      userId: demoUserId,
      createdAt: new Date(Date.now() - 86400000), // 1天前
      updatedAt: new Date(Date.now() - 86400000)
    },
    {
      _id: ObjectId(),
      title: '学习笔记',
      content: '# React Hooks 学习笔记\n\n## useState\n- 用于在函数组件中添加状态\n- 返回状态值和更新函数\n\n## useEffect\n- 用于处理副作用\n- 可以模拟生命周期方法\n\n## useContext\n- 用于消费 Context\n- 避免 prop drilling\n\n## 自定义 Hooks\n- 复用状态逻辑\n- 遵循 Hooks 规则',
      category: '个人',
      tags: ['学习', 'React', '前端'],
      color: '#d1fae5',
      isPinned: false,
      isArchived: false,
      userId: demoUserId,
      createdAt: new Date(Date.now() - 172800000), // 2天前
      updatedAt: new Date(Date.now() - 172800000)
    },
    {
      _id: ObjectId(),
      title: '会议记录',
      content: '# 产品规划会议\n\n**时间**: 2024年1月15日 14:00-16:00\n**参与人员**: 产品经理、开发团队、设计师\n\n## 讨论要点\n\n1. 用户反馈分析\n2. 新功能优先级\n3. 技术架构调整\n\n## 决定事项\n\n- 优先开发移动端适配\n- 增加协作功能\n- 改进搜索体验\n\n## 行动项\n\n- [ ] 制定详细开发计划\n- [ ] 设计原型图\n- [ ] 技术方案评估',
      category: '工作',
      tags: ['会议', '产品', '规划'],
      color: '#ffffff',
      isPinned: false,
      isArchived: true,
      userId: demoUserId,
      createdAt: new Date(Date.now() - 259200000), // 3天前
      updatedAt: new Date(Date.now() - 259200000)
    }
  ]);
  
  print('演示数据创建完成');
  print('演示账户:');
  print('  用户: <EMAIL> / demo123456');
  print('  管理员: <EMAIL> / admin123456');
}

print('MongoDB 初始化完成');
