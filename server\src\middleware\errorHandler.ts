import { Request, Response, NextFunction } from 'express';
import { ValidationError } from 'express-validator';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  err: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = { ...err };
  error.message = err.message;

  // 开发环境下打印错误堆栈
  if (process.env.NODE_ENV === 'development') {
    console.error('❌ 错误详情:', err);
  }

  // Mongoose 错误处理
  if (err.name === 'CastError') {
    const message = '资源未找到';
    error = new CustomError(message, 404);
  }

  // Mongoose 重复字段错误
  if (err.name === 'MongoServerError' && (err as any).code === 11000) {
    const message = '资源已存在';
    error = new CustomError(message, 400);
  }

  // Mongoose 验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values((err as any).errors).map((val: any) => val.message).join(', ');
    error = new CustomError(message, 400);
  }

  // JWT 错误处理
  if (err.name === 'JsonWebTokenError') {
    const message = '无效的访问令牌';
    error = new CustomError(message, 401);
  }

  if (err.name === 'TokenExpiredError') {
    const message = '访问令牌已过期';
    error = new CustomError(message, 401);
  }

  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message || '服务器内部错误',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
  });
};

export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) =>
  Promise.resolve(fn(req, res, next)).catch(next);
