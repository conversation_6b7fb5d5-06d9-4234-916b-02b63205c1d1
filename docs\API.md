# LoftNotes API 文档

## 概述

LoftNotes 提供了完整的 RESTful API，支持用户认证、便签管理、分类管理等功能。

## 基础信息

- **Base URL**: `http://localhost:5000/api`
- **认证方式**: Bearer <PERSON> (JWT)
- **数据格式**: JSON

## 认证

### 注册用户

```http
POST /auth/register
```

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "user": {
      "_id": "string",
      "username": "string",
      "email": "string",
      "role": "user",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    "token": "string",
    "refreshToken": "string"
  }
}
```

### 用户登录

```http
POST /auth/login
```

**请求体**:
```json
{
  "email": "string",
  "password": "string"
}
```

### 刷新令牌

```http
POST /auth/refresh
```

**请求体**:
```json
{
  "refreshToken": "string"
}
```

### 获取用户信息

```http
GET /auth/me
Authorization: Bearer <token>
```

## 便签管理

### 获取便签列表

```http
GET /notes
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类筛选
- `tags`: 标签筛选 (数组)
- `search`: 搜索关键词
- `sortBy`: 排序字段 (createdAt, updatedAt, title)
- `sortOrder`: 排序方向 (asc, desc)
- `isPinned`: 是否置顶
- `isArchived`: 是否归档

**响应**:
```json
{
  "success": true,
  "data": {
    "notes": [
      {
        "_id": "string",
        "title": "string",
        "content": "string",
        "category": "string",
        "tags": ["string"],
        "color": "#ffffff",
        "isPinned": false,
        "isArchived": false,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 创建便签

```http
POST /notes
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "title": "string",
  "content": "string",
  "category": "string",
  "tags": ["string"],
  "color": "#ffffff",
  "isPinned": false
}
```

### 更新便签

```http
PUT /notes/:id
Authorization: Bearer <token>
```

### 删除便签

```http
DELETE /notes/:id
Authorization: Bearer <token>
```

### 批量操作便签

```http
PATCH /notes/batch
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "ids": ["string"],
  "action": "pin|unpin|archive|unarchive|delete",
  "data": {}
}
```

## 分类管理

### 获取分类列表

```http
GET /categories
Authorization: Bearer <token>
```

### 创建分类

```http
POST /categories
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "string",
  "color": "#3B82F6"
}
```

### 更新分类

```http
PUT /categories/:id
Authorization: Bearer <token>
```

### 删除分类

```http
DELETE /categories/:id
Authorization: Bearer <token>
```

## 搜索功能

### 全局搜索

```http
GET /search?q=keyword&type=all
Authorization: Bearer <token>
```

**查询参数**:
- `q`: 搜索关键词
- `type`: 搜索类型 (all, notes, categories)
- `page`: 页码
- `limit`: 每页数量

### 搜索建议

```http
GET /search/suggestions?q=keyword
Authorization: Bearer <token>
```

### 获取热门标签

```http
GET /search/tags/popular
Authorization: Bearer <token>
```

## 导入导出

### 导出便签

```http
GET /export/notes?format=json&includeArchived=false
Authorization: Bearer <token>
```

**查询参数**:
- `format`: 导出格式 (json, csv, markdown)
- `includeArchived`: 是否包含归档便签

### 导入便签

```http
POST /export/notes
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "data": {
    "notes": [],
    "categories": []
  },
  "options": {
    "skipDuplicates": true,
    "updateExisting": false,
    "importCategories": true
  }
}
```

## 管理员功能

### 获取系统统计

```http
GET /admin/stats
Authorization: Bearer <admin_token>
```

### 获取用户列表

```http
GET /admin/users
Authorization: Bearer <admin_token>
```

### 更新用户状态

```http
PATCH /admin/users/:id/status
Authorization: Bearer <admin_token>
```

**请求体**:
```json
{
  "isActive": true
}
```

## 监控功能

### 健康检查

```http
GET /health
```

### 性能统计

```http
GET /monitoring/performance
Authorization: Bearer <admin_token>
```

### 系统指标

```http
GET /monitoring/metrics?timeRange=1h
Authorization: Bearer <admin_token>
```

## 错误响应

所有错误响应都遵循以下格式：

```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

### 常见错误码

- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率过高
- `500`: 服务器内部错误

## 限制说明

- API 请求频率限制: 100 请求/15分钟
- 登录请求频率限制: 5 请求/分钟
- 文件上传大小限制: 5MB
- 便签内容长度限制: 50,000 字符
- 标签数量限制: 10 个/便签
