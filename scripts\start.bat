@echo off
setlocal enabledelayedexpansion

REM LoftNotes Windows 启动脚本
REM 使用方法: scripts\start.bat [模式]

set MODE=%1
if "%MODE%"=="" set MODE=dev

echo [INFO] LoftNotes 启动脚本
echo [INFO] 模式: %MODE%

if "%MODE%"=="help" goto :show_help
if "%MODE%"=="dev" goto :start_dev
if "%MODE%"=="prod" goto :start_prod
if "%MODE%"=="docker" goto :start_docker
if "%MODE%"=="stop" goto :stop_all
if "%MODE%"=="status" goto :show_status

echo [ERROR] 未知模式: %MODE%
goto :show_help

:show_help
echo.
echo LoftNotes Windows 启动脚本
echo.
echo 使用方法: scripts\start.bat [模式]
echo.
echo 模式:
echo   dev          开发模式 (默认)
echo   prod         生产模式
echo   docker       Docker模式
echo   stop         停止服务
echo   status       查看状态
echo   help         显示此帮助信息
echo.
goto :end

:start_dev
echo [INFO] 启动开发模式...

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装
    goto :end
)

REM 检查依赖
if not exist "client\node_modules" (
    echo [INFO] 安装前端依赖...
    cd client
    call npm install
    cd ..
)

if not exist "server\node_modules" (
    echo [INFO] 安装后端依赖...
    cd server
    call npm install
    cd ..
)

REM 启动后端服务
echo [INFO] 启动后端服务...
cd server
start "LoftNotes Backend" cmd /k "npm run dev"
cd ..

REM 等待后端启动
timeout /t 3 /nobreak >nul

REM 启动前端服务
echo [INFO] 启动前端服务...
cd client
start "LoftNotes Frontend" cmd /k "npm run dev"
cd ..

echo [SUCCESS] 开发服务启动完成!
echo [INFO] 前端: http://localhost:3000
echo [INFO] 后端: http://localhost:5000
goto :end

:start_prod
echo [INFO] 启动生产模式...

REM 构建应用
echo [INFO] 构建前端...
cd client
call npm run build
cd ..

echo [INFO] 构建后端...
cd server
call npm run build
cd ..

REM 启动生产服务
echo [INFO] 启动生产服务...
cd server
set NODE_ENV=production
start "LoftNotes Production" cmd /k "node dist\index.js"
cd ..

echo [SUCCESS] 生产服务启动完成!
echo [INFO] 应用: http://localhost:5000
goto :end

:start_docker
echo [INFO] 启动Docker服务...

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装
    goto :end
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装
    goto :end
)

docker-compose up -d

echo [SUCCESS] Docker服务启动完成!
echo [INFO] 应用: http://localhost
goto :end

:stop_all
echo [INFO] 停止所有服务...

REM 停止Docker服务
docker-compose down >nul 2>&1

REM 停止Node.js进程
taskkill /f /im node.exe >nul 2>&1

echo [SUCCESS] 所有服务已停止
goto :end

:show_status
echo [INFO] 服务状态:

REM 检查端口占用
netstat -an | findstr ":3000" >nul 2>&1
if not errorlevel 1 (
    echo   端口 3000: 占用中
) else (
    echo   端口 3000: 空闲
)

netstat -an | findstr ":5000" >nul 2>&1
if not errorlevel 1 (
    echo   端口 5000: 占用中
) else (
    echo   端口 5000: 空闲
)

REM 检查Docker
docker-compose ps >nul 2>&1
if not errorlevel 1 (
    echo   Docker: 运行中
) else (
    echo   Docker: 已停止
)

goto :end

:end
echo.
pause
