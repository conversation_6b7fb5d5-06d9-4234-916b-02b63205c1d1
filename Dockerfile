# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package.json文件
COPY package*.json ./
COPY client/package*.json ./client/
COPY server/package*.json ./server/

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建前端
WORKDIR /app/client
RUN npm ci
RUN npm run build

# 构建后端
WORKDIR /app/server
RUN npm ci
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S loftnotes -u 1001

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder --chown=loftnotes:nodejs /app/server/dist ./server/dist
COPY --from=builder --chown=loftnotes:nodejs /app/server/package*.json ./server/
COPY --from=builder --chown=loftnotes:nodejs /app/client/dist ./client/dist
COPY --from=builder --chown=loftnotes:nodejs /app/shared ./shared

# 安装生产依赖
WORKDIR /app/server
RUN npm ci --only=production && npm cache clean --force

# 创建必要的目录
RUN mkdir -p /app/uploads && chown loftnotes:nodejs /app/uploads

# 切换到非root用户
USER loftnotes

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["node", "dist/index.js"]
