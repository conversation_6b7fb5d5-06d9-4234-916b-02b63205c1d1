import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
}

class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, string> = new Map(); // userId -> socketId

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // 身份验证中间件
    this.io.use(async (socket: any, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
        
        if (!token) {
          return next(new Error('未提供认证令牌'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user || !user.isActive) {
          return next(new Error('用户不存在或已被禁用'));
        }

        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        next(new Error('认证失败'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`🔌 用户连接: ${socket.user?.username} (${socket.userId})`);
      
      // 记录用户连接
      if (socket.userId) {
        this.connectedUsers.set(socket.userId, socket.id);
        
        // 加入用户专属房间
        socket.join(`user:${socket.userId}`);
        
        // 发送连接成功消息
        socket.emit('connected', {
          message: '连接成功',
          user: socket.user,
          timestamp: new Date().toISOString(),
        });

        // 发送在线状态给其他用户（如果需要）
        socket.broadcast.emit('user:online', {
          userId: socket.userId,
          username: socket.user?.username,
        });
      }

      // 处理便签相关事件
      this.setupNoteEvents(socket);
      
      // 处理分类相关事件
      this.setupCategoryEvents(socket);
      
      // 处理通知事件
      this.setupNotificationEvents(socket);

      // 处理断开连接
      socket.on('disconnect', (reason) => {
        console.log(`🔌 用户断开连接: ${socket.user?.username} (${reason})`);
        
        if (socket.userId) {
          this.connectedUsers.delete(socket.userId);
          
          // 发送离线状态给其他用户（如果需要）
          socket.broadcast.emit('user:offline', {
            userId: socket.userId,
            username: socket.user?.username,
          });
        }
      });

      // 处理错误
      socket.on('error', (error) => {
        console.error(`❌ WebSocket 错误 (${socket.user?.username}):`, error);
      });
    });

    console.log('🚀 WebSocket 服务已启动');
  }

  private setupNoteEvents(socket: AuthenticatedSocket) {
    // 便签创建事件
    socket.on('note:create', (data) => {
      console.log(`📝 用户 ${socket.user?.username} 创建便签:`, data.title);
      
      // 广播给同一用户的其他设备
      socket.to(`user:${socket.userId}`).emit('note:created', {
        note: data,
        timestamp: new Date().toISOString(),
      });
    });

    // 便签更新事件
    socket.on('note:update', (data) => {
      console.log(`📝 用户 ${socket.user?.username} 更新便签:`, data.id);
      
      // 广播给同一用户的其他设备
      socket.to(`user:${socket.userId}`).emit('note:updated', {
        note: data,
        timestamp: new Date().toISOString(),
      });
    });

    // 便签删除事件
    socket.on('note:delete', (data) => {
      console.log(`🗑️ 用户 ${socket.user?.username} 删除便签:`, data.id);
      
      // 广播给同一用户的其他设备
      socket.to(`user:${socket.userId}`).emit('note:deleted', {
        noteId: data.id,
        timestamp: new Date().toISOString(),
      });
    });

    // 便签同步请求
    socket.on('note:sync', () => {
      console.log(`🔄 用户 ${socket.user?.username} 请求同步便签`);
      
      // 触发同步事件
      socket.emit('note:sync-requested', {
        timestamp: new Date().toISOString(),
      });
    });
  }

  private setupCategoryEvents(socket: AuthenticatedSocket) {
    // 分类创建事件
    socket.on('category:create', (data) => {
      console.log(`📁 用户 ${socket.user?.username} 创建分类:`, data.name);
      
      socket.to(`user:${socket.userId}`).emit('category:created', {
        category: data,
        timestamp: new Date().toISOString(),
      });
    });

    // 分类更新事件
    socket.on('category:update', (data) => {
      console.log(`📁 用户 ${socket.user?.username} 更新分类:`, data.id);
      
      socket.to(`user:${socket.userId}`).emit('category:updated', {
        category: data,
        timestamp: new Date().toISOString(),
      });
    });

    // 分类删除事件
    socket.on('category:delete', (data) => {
      console.log(`🗑️ 用户 ${socket.user?.username} 删除分类:`, data.id);
      
      socket.to(`user:${socket.userId}`).emit('category:deleted', {
        categoryId: data.id,
        timestamp: new Date().toISOString(),
      });
    });
  }

  private setupNotificationEvents(socket: AuthenticatedSocket) {
    // 心跳检测
    socket.on('ping', () => {
      socket.emit('pong', {
        timestamp: new Date().toISOString(),
      });
    });

    // 通知确认
    socket.on('notification:read', (data) => {
      console.log(`🔔 用户 ${socket.user?.username} 已读通知:`, data.notificationId);
    });
  }

  // 公共方法：发送通知给特定用户
  public sendNotificationToUser(userId: string, notification: any): boolean {
    const socketId = this.connectedUsers.get(userId);
    
    if (socketId) {
      this.io.to(socketId).emit('notification', {
        ...notification,
        timestamp: new Date().toISOString(),
      });
      
      console.log(`🔔 通知已发送给用户: ${userId}`);
      return true;
    }
    
    console.log(`⚠️ 用户 ${userId} 不在线，通知未发送`);
    return false;
  }

  // 公共方法：广播系统通知
  public broadcastSystemNotification(notification: any): void {
    this.io.emit('system:notification', {
      ...notification,
      timestamp: new Date().toISOString(),
    });
    
    console.log('📢 系统通知已广播');
  }

  // 公共方法：发送便签同步事件
  public syncNoteToUser(userId: string, noteData: any, action: 'create' | 'update' | 'delete'): boolean {
    const socketId = this.connectedUsers.get(userId);
    
    if (socketId) {
      this.io.to(socketId).emit(`note:${action}d`, {
        note: noteData,
        timestamp: new Date().toISOString(),
      });
      
      console.log(`🔄 便签同步已发送给用户: ${userId} (${action})`);
      return true;
    }
    
    return false;
  }

  // 公共方法：获取在线用户数量
  public getOnlineUsersCount(): number {
    return this.connectedUsers.size;
  }

  // 公共方法：获取在线用户列表
  public getOnlineUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  // 公共方法：检查用户是否在线
  public isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  // 公共方法：强制断开用户连接
  public disconnectUser(userId: string, reason?: string): boolean {
    const socketId = this.connectedUsers.get(userId);
    
    if (socketId) {
      const socket = this.io.sockets.sockets.get(socketId);
      if (socket) {
        socket.disconnect(true);
        console.log(`🔌 强制断开用户连接: ${userId} (${reason || '管理员操作'})`);
        return true;
      }
    }
    
    return false;
  }
}

export default WebSocketService;
