import express from 'express';
import { validationResult } from 'express-validator';
import { Note } from '../models/Note';
import { noteValidation, commonValidation } from '../utils/validation';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { protect } from '../middleware/auth';
import { cache, notesCacheKey, clearNotesCache } from '../middleware/cache';

const router = express.Router();

// 所有路由都需要认证
router.use(protect);

// 获取便签列表（带缓存）
router.get('/', [
  cache({
    ttl: 300, // 5分钟缓存
    keyGenerator: notesCacheKey,
    condition: (req, res) => res.statusCode === 200,
  }),
  ...noteValidation.query,
], asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const {
    page = 1,
    limit = 20,
    category,
    tags,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    isPinned,
    isArchived = false,
  } = req.query;

  // 构建查询条件
  const query: any = { userId, isArchived };

  if (category) {
    query.category = category;
  }

  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : [tags];
    query.tags = { $in: tagArray };
  }

  if (search) {
    query.$text = { $search: search as string };
  }

  if (isPinned !== undefined) {
    query.isPinned = isPinned === 'true';
  }

  // 排序
  const sortOptions: any = {};
  sortOptions[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

  // 如果按置顶排序，先按置顶状态排序，再按指定字段排序
  if (isPinned === undefined) {
    sortOptions.isPinned = -1;
  }

  // 分页
  const skip = (Number(page) - 1) * Number(limit);

  // 执行查询
  const [notes, total] = await Promise.all([
    Note.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(Number(limit))
      .lean(),
    Note.countDocuments(query),
  ]);

  res.json({
    success: true,
    data: {
      notes,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// 获取单个便签
router.get('/:id', commonValidation.mongoId, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;

  const note = await Note.findOne({ _id: id, userId });

  if (!note) {
    throw new CustomError('便签不存在', 404);
  }

  res.json({
    success: true,
    data: { note },
  });
}));

// 创建便签
router.post('/', noteValidation.create, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { title, content, category, tags, color, isPinned } = req.body;

  const note = await Note.create({
    title,
    content,
    category,
    tags: tags || [],
    color: color || '#ffffff',
    isPinned: isPinned || false,
    userId,
  });

  // 清除用户的便签缓存
  await clearNotesCache(userId);

  res.status(201).json({
    success: true,
    message: '便签创建成功',
    data: { note },
  });
}));

// 更新便签
router.put('/:id', [
  ...commonValidation.mongoId,
  ...noteValidation.update,
], asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;

  const note = await Note.findOneAndUpdate(
    { _id: id, userId },
    req.body,
    { new: true, runValidators: true }
  );

  if (!note) {
    throw new CustomError('便签不存在', 404);
  }

  res.json({
    success: true,
    message: '便签更新成功',
    data: { note },
  });
}));

// 删除便签
router.delete('/:id', commonValidation.mongoId, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;

  const note = await Note.findOneAndDelete({ _id: id, userId });

  if (!note) {
    throw new CustomError('便签不存在', 404);
  }

  res.json({
    success: true,
    message: '便签删除成功',
  });
}));

// 批量操作便签
router.patch('/batch', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;
  const { ids, action, data } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    throw new CustomError('便签ID列表不能为空', 400);
  }

  if (!action) {
    throw new CustomError('操作类型不能为空', 400);
  }

  let updateData: any = {};

  switch (action) {
    case 'archive':
      updateData = { isArchived: true };
      break;
    case 'unarchive':
      updateData = { isArchived: false };
      break;
    case 'pin':
      updateData = { isPinned: true };
      break;
    case 'unpin':
      updateData = { isPinned: false };
      break;
    case 'update':
      if (!data) {
        throw new CustomError('更新数据不能为空', 400);
      }
      updateData = data;
      break;
    case 'delete':
      const deleteResult = await Note.deleteMany({
        _id: { $in: ids },
        userId,
      });
      return res.json({
        success: true,
        message: `成功删除 ${deleteResult.deletedCount} 个便签`,
        data: { deletedCount: deleteResult.deletedCount },
      });
    default:
      throw new CustomError('不支持的操作类型', 400);
  }

  const result = await Note.updateMany(
    { _id: { $in: ids }, userId },
    updateData
  );

  res.json({
    success: true,
    message: `成功更新 ${result.modifiedCount} 个便签`,
    data: { modifiedCount: result.modifiedCount },
  });
}));

// 获取便签统计信息
router.get('/stats/overview', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;

  const [
    totalNotes,
    pinnedNotes,
    archivedNotes,
    categories,
    recentNotes,
  ] = await Promise.all([
    Note.countDocuments({ userId, isArchived: false }),
    Note.countDocuments({ userId, isPinned: true, isArchived: false }),
    Note.countDocuments({ userId, isArchived: true }),
    Note.distinct('category', { userId, category: { $ne: null } }),
    Note.find({ userId, isArchived: false })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title createdAt')
      .lean(),
  ]);

  res.json({
    success: true,
    data: {
      totalNotes,
      pinnedNotes,
      archivedNotes,
      categoriesCount: categories.length,
      recentNotes,
    },
  });
}));

module.exports = router;
