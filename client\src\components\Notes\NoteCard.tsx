import React from 'react';
import { Pin, Archive, Trash2, Edit, MoreHorizontal } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface Note {
  _id: string;
  title: string;
  content: string;
  category?: string;
  tags: string[];
  color: string;
  isPinned: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
}

interface NoteCardProps {
  note: Note;
  onEdit: (note: Note) => void;
  onDelete: (id: string) => void;
  onPin: (id: string, isPinned: boolean) => void;
  onArchive: (id: string, isArchived: boolean) => void;
  onClick?: (note: Note) => void;
}

export const NoteCard: React.FC<NoteCardProps> = ({
  note,
  onEdit,
  onDelete,
  onPin,
  onArchive,
  onClick,
}) => {
  const handleCardClick = (e: React.MouseEvent) => {
    // 防止点击操作按钮时触发卡片点击
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    onClick?.(note);
  };

  const getContentPreview = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <Card
      className={cn(
        "group relative cursor-pointer transition-all duration-200 hover:shadow-md",
        note.isPinned && "ring-2 ring-primary/20",
        note.isArchived && "opacity-60"
      )}
      style={{ backgroundColor: note.color !== '#ffffff' ? note.color : undefined }}
      onClick={handleCardClick}
    >
      {/* 置顶标识 */}
      {note.isPinned && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <Pin className="h-3 w-3" />
          </div>
        </div>
      )}

      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <h3 className="font-semibold text-sm line-clamp-2 flex-1 mr-2">
            {note.title}
          </h3>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(note)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onPin(note._id, !note.isPinned)}>
                <Pin className="mr-2 h-4 w-4" />
                {note.isPinned ? '取消置顶' : '置顶'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onArchive(note._id, !note.isArchived)}>
                <Archive className="mr-2 h-4 w-4" />
                {note.isArchived ? '取消归档' : '归档'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete(note._id)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 内容预览 */}
        <p className="text-sm text-muted-foreground mb-3 line-clamp-4">
          {getContentPreview(note.content)}
        </p>

        {/* 分类 */}
        {note.category && (
          <div className="mb-2">
            <Badge variant="secondary" className="text-xs">
              {note.category}
            </Badge>
          </div>
        )}

        {/* 标签 */}
        {note.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {note.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                #{tag}
              </Badge>
            ))}
            {note.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{note.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* 时间信息 */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>
            {format(new Date(note.updatedAt), 'MM-dd HH:mm', { locale: zhCN })}
          </span>
          {note.isArchived && (
            <Badge variant="secondary" className="text-xs">
              已归档
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
