#!/bin/bash

# LoftNotes 启动脚本
# 使用方法: ./scripts/start.sh [模式]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认模式
MODE=${1:-dev}

# 显示帮助信息
show_help() {
    echo "LoftNotes 启动脚本"
    echo ""
    echo "使用方法: $0 [模式]"
    echo ""
    echo "模式:"
    echo "  dev          开发模式 (默认)"
    echo "  prod         生产模式"
    echo "  docker       Docker模式"
    echo "  stop         停止服务"
    echo "  restart      重启服务"
    echo "  status       查看状态"
    echo "  logs         查看日志"
    echo "  help         显示此帮助信息"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查端口是否被占用
check_ports() {
    log_info "检查端口占用..."
    
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 3000 已被占用"
    fi
    
    if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 5000 已被占用"
    fi
}

# 启动开发模式
start_dev() {
    log_info "启动开发模式..."
    
    check_dependencies
    check_ports
    
    # 检查是否已安装依赖
    if [ ! -d "client/node_modules" ]; then
        log_info "安装前端依赖..."
        cd client && npm install && cd ..
    fi
    
    if [ ! -d "server/node_modules" ]; then
        log_info "安装后端依赖..."
        cd server && npm install && cd ..
    fi
    
    # 启动后端服务
    log_info "启动后端服务..."
    cd server
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 启动前端服务
    log_info "启动前端服务..."
    cd client
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # 保存进程ID
    echo $BACKEND_PID > .backend.pid
    echo $FRONTEND_PID > .frontend.pid
    
    log_success "开发服务启动完成!"
    log_info "前端: http://localhost:3000"
    log_info "后端: http://localhost:5000"
    log_info "按 Ctrl+C 停止服务"
    
    # 等待用户中断
    trap 'stop_dev' INT
    wait
}

# 停止开发模式
stop_dev() {
    log_info "停止开发服务..."
    
    if [ -f .backend.pid ]; then
        BACKEND_PID=$(cat .backend.pid)
        kill $BACKEND_PID 2>/dev/null || true
        rm .backend.pid
    fi
    
    if [ -f .frontend.pid ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        kill $FRONTEND_PID 2>/dev/null || true
        rm .frontend.pid
    fi
    
    # 强制杀死相关进程
    pkill -f "vite" 2>/dev/null || true
    pkill -f "node.*server" 2>/dev/null || true
    
    log_success "开发服务已停止"
}

# 启动生产模式
start_prod() {
    log_info "启动生产模式..."
    
    check_dependencies
    
    # 构建应用
    log_info "构建应用..."
    cd client && npm run build && cd ..
    cd server && npm run build && cd ..
    
    # 启动生产服务
    log_info "启动生产服务..."
    cd server
    NODE_ENV=production node dist/index.js &
    PROD_PID=$!
    cd ..
    
    echo $PROD_PID > .prod.pid
    
    log_success "生产服务启动完成!"
    log_info "应用: http://localhost:5000"
}

# 停止生产模式
stop_prod() {
    log_info "停止生产服务..."
    
    if [ -f .prod.pid ]; then
        PROD_PID=$(cat .prod.pid)
        kill $PROD_PID 2>/dev/null || true
        rm .prod.pid
    fi
    
    pkill -f "node.*dist/index.js" 2>/dev/null || true
    
    log_success "生产服务已停止"
}

# Docker模式
start_docker() {
    log_info "启动Docker服务..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    docker-compose up -d
    
    log_success "Docker服务启动完成!"
    log_info "应用: http://localhost"
}

# 停止Docker服务
stop_docker() {
    log_info "停止Docker服务..."
    docker-compose down
    log_success "Docker服务已停止"
}

# 查看状态
show_status() {
    log_info "服务状态:"
    
    # 检查开发模式
    if [ -f .backend.pid ] || [ -f .frontend.pid ]; then
        echo "  开发模式: 运行中"
    else
        echo "  开发模式: 已停止"
    fi
    
    # 检查生产模式
    if [ -f .prod.pid ]; then
        echo "  生产模式: 运行中"
    else
        echo "  生产模式: 已停止"
    fi
    
    # 检查Docker
    if docker-compose ps | grep -q "Up"; then
        echo "  Docker模式: 运行中"
    else
        echo "  Docker模式: 已停止"
    fi
    
    # 检查端口
    echo ""
    log_info "端口占用:"
    lsof -i :3000 2>/dev/null | grep LISTEN || echo "  端口 3000: 空闲"
    lsof -i :5000 2>/dev/null | grep LISTEN || echo "  端口 5000: 空闲"
}

# 查看日志
show_logs() {
    log_info "查看日志..."
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose logs -f
    else
        log_warning "Docker服务未运行"
        if [ -f "server/logs/app.log" ]; then
            tail -f server/logs/app.log
        else
            log_warning "未找到日志文件"
        fi
    fi
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    
    case $MODE in
        dev)
            stop_dev
            sleep 2
            start_dev
            ;;
        prod)
            stop_prod
            sleep 2
            start_prod
            ;;
        docker)
            stop_docker
            sleep 2
            start_docker
            ;;
        *)
            log_error "未知模式: $MODE"
            exit 1
            ;;
    esac
}

# 主执行逻辑
case $MODE in
    dev)
        start_dev
        ;;
    prod)
        start_prod
        ;;
    docker)
        start_docker
        ;;
    stop)
        stop_dev
        stop_prod
        stop_docker
        ;;
    restart)
        MODE=${2:-dev}
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    help)
        show_help
        ;;
    *)
        log_error "未知模式: $MODE"
        show_help
        exit 1
        ;;
esac
