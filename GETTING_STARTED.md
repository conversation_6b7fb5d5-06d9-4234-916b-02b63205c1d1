# 🚀 LoftNotes 快速开始指南

欢迎使用 LoftNotes！这份指南将帮助您快速搭建和运行这个现代化的电子便签系统。

## 📋 系统要求

### 必需环境
- **Node.js** >= 18.0.0
- **npm** >= 8.0.0
- **MongoDB** >= 6.0

### 可选环境
- **Redis** >= 7.0 (用于缓存和队列)
- **Docker** & **Docker Compose** (用于容器化部署)

## 🛠️ 安装步骤

### 方式一：本地开发环境

1. **克隆项目**
```bash
git clone <repository-url>
cd loftnotes
```

2. **安装依赖**
```bash
# 安装前端依赖
cd client
npm install
cd ..

# 安装后端依赖
cd server
npm install
cd ..
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp server/.env.example server/.env
cp client/.env.example client/.env

# 编辑配置文件
nano server/.env
```

4. **启动数据库**
```bash
# 如果使用Docker
docker run -d --name mongodb -p 27017:27017 mongo:6.0

# 如果使用Redis (可选)
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

5. **启动应用**
```bash
# 使用启动脚本 (推荐)
chmod +x scripts/start.sh
./scripts/start.sh dev

# 或者手动启动
# 终端1: 启动后端
cd server && npm run dev

# 终端2: 启动前端
cd client && npm run dev
```

6. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:5000
- API文档: http://localhost:5000/health

### 方式二：Docker 一键部署

1. **克隆项目**
```bash
git clone <repository-url>
cd loftnotes
```

2. **启动服务**
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

3. **访问应用**
- 应用: http://localhost
- 监控面板: http://localhost:3001 (用户名: admin, 密码: admin)

## 🎯 演示账户

系统预置了演示账户，您可以直接使用：

- **普通用户**: <EMAIL> / demo123456
- **管理员**: <EMAIL> / admin123456

## 📖 基本使用

### 1. 用户注册/登录
- 访问 http://localhost:3000
- 点击"注册"创建新账户或使用演示账户登录

### 2. 创建便签
- 点击右上角"新建便签"按钮
- 输入标题和内容
- 选择分类和标签
- 点击"保存"

### 3. 管理便签
- **编辑**: 点击便签卡片进入编辑模式
- **置顶**: 点击便签右上角的更多菜单选择"置顶"
- **归档**: 选择"归档"将便签移至归档区
- **删除**: 选择"删除"永久删除便签

### 4. 搜索功能
- 使用顶部搜索框输入关键词
- 支持标题、内容、标签搜索
- 使用左侧分类筛选

### 5. 管理员功能 (需要管理员权限)
- 点击左侧"管理面板"
- 查看系统统计信息
- 管理用户账户
- 监控系统性能

## 🔧 配置说明

### 环境变量配置

**后端配置 (server/.env)**
```env
# 基本配置
PORT=5000
NODE_ENV=development

# 数据库
MONGODB_URI=mongodb://localhost:27017/loftnotes

# JWT密钥 (生产环境必须修改)
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-refresh-secret-key

# Redis (可选)
REDIS_HOST=localhost
REDIS_PORT=6379

# CORS
CORS_ORIGIN=http://localhost:3000
```

**前端配置 (client/.env)**
```env
# API地址
VITE_API_BASE_URL=http://localhost:5000/api
VITE_WS_URL=http://localhost:5000

# 应用信息
VITE_APP_NAME=LoftNotes
VITE_APP_VERSION=1.0.0
```

### 数据库配置

MongoDB 会自动创建以下集合：
- `users` - 用户信息
- `notes` - 便签数据
- `categories` - 分类信息

首次启动时会自动创建索引和演示数据。

## 🚀 生产部署

### 使用部署脚本
```bash
# 赋予执行权限
chmod +x scripts/deploy.sh

# 部署到生产环境
./scripts/deploy.sh production
```

### 手动部署
```bash
# 1. 构建前端
cd client && npm run build && cd ..

# 2. 构建后端
cd server && npm run build && cd ..

# 3. 设置生产环境变量
export NODE_ENV=production
export JWT_SECRET=your-production-secret
export MONGODB_URI=your-production-mongodb-uri

# 4. 启动服务
cd server && node dist/index.js
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
lsof -i :3000
lsof -i :5000

# 杀死进程
kill -9 <PID>
```

2. **数据库连接失败**
```bash
# 检查MongoDB状态
docker ps | grep mongo

# 重启MongoDB
docker restart mongodb
```

3. **依赖安装失败**
```bash
# 清除缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

4. **权限问题**
```bash
# 赋予脚本执行权限
chmod +x scripts/*.sh

# 修复文件权限
sudo chown -R $USER:$USER .
```

### 查看日志

```bash
# 开发模式日志
./scripts/start.sh logs

# Docker模式日志
docker-compose logs -f

# 生产模式日志
tail -f server/logs/app.log
```

## 📞 获取帮助

如果您遇到问题，可以：

1. 查看 [API文档](docs/API.md)
2. 检查 [GitHub Issues](https://github.com/your-username/loftnotes/issues)
3. 联系开发团队

## 🎉 开始使用

现在您已经成功搭建了 LoftNotes，开始创建您的第一个便签吧！

---

**提示**: 建议在生产环境中修改默认的JWT密钥和数据库密码，确保系统安全。
