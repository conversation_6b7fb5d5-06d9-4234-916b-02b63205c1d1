import { body, query, param, ValidationChain } from 'express-validator';

// 用户验证规则
export const userValidation = {
  register: [
    body('username')
      .trim()
      .isLength({ min: 2, max: 30 })
      .withMessage('用户名长度必须在2-30个字符之间')
      .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
      .withMessage('用户名只能包含字母、数字、下划线和中文'),
    
    body('email')
      .isEmail()
      .withMessage('请输入有效的邮箱地址')
      .normalizeEmail(),
    
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码至少6个字符')
      .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
      .withMessage('密码必须包含至少一个字母和一个数字'),
  ],

  login: [
    body('email')
      .isEmail()
      .withMessage('请输入有效的邮箱地址')
      .normalizeEmail(),
    
    body('password')
      .notEmpty()
      .withMessage('密码不能为空'),
  ],

  updateProfile: [
    body('username')
      .optional()
      .trim()
      .isLength({ min: 2, max: 30 })
      .withMessage('用户名长度必须在2-30个字符之间')
      .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
      .withMessage('用户名只能包含字母、数字、下划线和中文'),
    
    body('avatar')
      .optional()
      .isURL()
      .withMessage('头像必须是有效的URL'),
  ],
};

// 便签验证规则
export const noteValidation = {
  create: [
    body('title')
      .trim()
      .notEmpty()
      .withMessage('便签标题不能为空')
      .isLength({ max: 200 })
      .withMessage('标题不能超过200个字符'),
    
    body('content')
      .trim()
      .notEmpty()
      .withMessage('便签内容不能为空')
      .isLength({ max: 50000 })
      .withMessage('内容不能超过50000个字符'),
    
    body('category')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('分类名称不能超过50个字符'),
    
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('标签数量不能超过10个'),
    
    body('tags.*')
      .optional()
      .trim()
      .isLength({ max: 30 })
      .withMessage('标签不能超过30个字符'),
    
    body('color')
      .optional()
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('请输入有效的颜色值'),
    
    body('isPinned')
      .optional()
      .isBoolean()
      .withMessage('isPinned必须是布尔值'),
  ],

  update: [
    body('title')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('便签标题不能为空')
      .isLength({ max: 200 })
      .withMessage('标题不能超过200个字符'),
    
    body('content')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('便签内容不能为空')
      .isLength({ max: 50000 })
      .withMessage('内容不能超过50000个字符'),
    
    body('category')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('分类名称不能超过50个字符'),
    
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('标签数量不能超过10个'),
    
    body('tags.*')
      .optional()
      .trim()
      .isLength({ max: 30 })
      .withMessage('标签不能超过30个字符'),
    
    body('color')
      .optional()
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('请输入有效的颜色值'),
    
    body('isPinned')
      .optional()
      .isBoolean()
      .withMessage('isPinned必须是布尔值'),
    
    body('isArchived')
      .optional()
      .isBoolean()
      .withMessage('isArchived必须是布尔值'),
  ],

  query: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    
    query('sortBy')
      .optional()
      .isIn(['createdAt', 'updatedAt', 'title'])
      .withMessage('排序字段只能是createdAt、updatedAt或title'),
    
    query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('排序方向只能是asc或desc'),
    
    query('isPinned')
      .optional()
      .isBoolean()
      .withMessage('isPinned必须是布尔值'),
    
    query('isArchived')
      .optional()
      .isBoolean()
      .withMessage('isArchived必须是布尔值'),
  ],
};

// 分类验证规则
export const categoryValidation = {
  create: [
    body('name')
      .trim()
      .notEmpty()
      .withMessage('分类名称不能为空')
      .isLength({ max: 50 })
      .withMessage('分类名称不能超过50个字符'),
    
    body('color')
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('请输入有效的颜色值'),
  ],

  update: [
    body('name')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('分类名称不能为空')
      .isLength({ max: 50 })
      .withMessage('分类名称不能超过50个字符'),
    
    body('color')
      .optional()
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('请输入有效的颜色值'),
  ],
};

// 通用验证规则
export const commonValidation = {
  mongoId: [
    param('id')
      .isMongoId()
      .withMessage('无效的ID格式'),
  ],
};
