import React, { useState, useEffect } from 'react';
import { Users, FileText, Folder, TrendingUp, Activity, Shield } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { adminAPI } from '@/lib/api';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { toast } from 'react-hot-toast';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalNotes: number;
  totalCategories: number;
  newUsersThisMonth: number;
  notesCreatedThisMonth: number;
  userGrowthRate: string;
  noteGrowthRate: string;
}

interface User {
  _id: string;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  notesCount?: number;
}

export const AdminPanel: React.FC = () => {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'notes'>('overview');

  useEffect(() => {
    fetchAdminData();
  }, []);

  const fetchAdminData = async () => {
    setIsLoading(true);
    try {
      const [statsResponse, usersResponse] = await Promise.all([
        adminAPI.getStats(),
        adminAPI.getUsers({ limit: 10 }),
      ]);

      setStats(statsResponse.data.data);
      setUsers(usersResponse.data.data.users);
    } catch (error) {
      toast.error('获取管理数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleUserStatus = async (userId: string, isActive: boolean) => {
    try {
      await adminAPI.updateUserStatus(userId, !isActive);
      toast.success(`用户已${!isActive ? '启用' : '禁用'}`);
      fetchAdminData(); // 重新获取数据
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleChangeUserRole = async (userId: string, currentRole: string) => {
    const newRole = currentRole === 'admin' ? 'user' : 'admin';
    
    if (window.confirm(`确定要将用户角色改为${newRole === 'admin' ? '管理员' : '普通用户'}吗？`)) {
      try {
        await adminAPI.updateUserRole(userId, newRole);
        toast.success('用户角色更新成功');
        fetchAdminData(); // 重新获取数据
      } catch (error) {
        toast.error('操作失败，请重试');
      }
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  const renderOverview = () => (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              活跃用户: {stats?.activeUsers}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总便签数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalNotes}</div>
            <p className="text-xs text-muted-foreground">
              本月新增: {stats?.notesCreatedThisMonth}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">分类数量</CardTitle>
            <Folder className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalCategories}</div>
            <p className="text-xs text-muted-foreground">
              用户创建的分类
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">用户增长</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.userGrowthRate}</div>
            <p className="text-xs text-muted-foreground">
              本月新用户: {stats?.newUsersThisMonth}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 最近用户 */}
      <Card>
        <CardHeader>
          <CardTitle>最近注册用户</CardTitle>
          <CardDescription>
            最新注册的用户列表
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.slice(0, 5).map((user) => (
              <div key={user._id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium">
                      {user.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">{user.username}</p>
                    <p className="text-xs text-muted-foreground">{user.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                    {user.role === 'admin' ? '管理员' : '用户'}
                  </Badge>
                  <Badge variant={user.isActive ? 'default' : 'destructive'}>
                    {user.isActive ? '活跃' : '禁用'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderUsers = () => (
    <Card>
      <CardHeader>
        <CardTitle>用户管理</CardTitle>
        <CardDescription>
          管理系统中的所有用户
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {users.map((user) => (
            <div key={user._id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="font-medium">{user.username}</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                  <p className="text-xs text-muted-foreground">
                    注册时间: {new Date(user.createdAt).toLocaleDateString('zh-CN')}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                  {user.role === 'admin' ? '管理员' : '用户'}
                </Badge>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleChangeUserRole(user._id, user.role)}
                >
                  <Shield className="h-4 w-4 mr-1" />
                  切换角色
                </Button>
                
                <Button
                  variant={user.isActive ? "destructive" : "default"}
                  size="sm"
                  onClick={() => handleToggleUserStatus(user._id, user.isActive)}
                >
                  <Activity className="h-4 w-4 mr-1" />
                  {user.isActive ? '禁用' : '启用'}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">管理面板</h1>
        <p className="text-muted-foreground">系统管理和监控</p>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6">
        <Button
          variant={activeTab === 'overview' ? 'default' : 'outline'}
          onClick={() => setActiveTab('overview')}
        >
          概览
        </Button>
        <Button
          variant={activeTab === 'users' ? 'default' : 'outline'}
          onClick={() => setActiveTab('users')}
        >
          用户管理
        </Button>
      </div>

      {/* 内容区域 */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'users' && renderUsers()}
    </div>
  );
};
