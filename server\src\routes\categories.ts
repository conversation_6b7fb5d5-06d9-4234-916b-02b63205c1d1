import express from 'express';
import { validationResult } from 'express-validator';
import { Category } from '../models/Category';
import { Note } from '../models/Note';
import { categoryValidation, commonValidation } from '../utils/validation';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { protect } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(protect);

// 获取分类列表
router.get('/', asyncHandler(async (req, res) => {
  const userId = (req as any).user._id;

  const categories = await Category.find({ userId })
    .sort({ createdAt: -1 })
    .populate('notesCount')
    .lean();

  res.json({
    success: true,
    data: { categories },
  });
}));

// 获取单个分类
router.get('/:id', commonValidation.mongoId, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;

  const category = await Category.findOne({ _id: id, userId })
    .populate('notesCount');

  if (!category) {
    throw new CustomError('分类不存在', 404);
  }

  res.json({
    success: true,
    data: { category },
  });
}));

// 创建分类
router.post('/', categoryValidation.create, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { name, color } = req.body;

  // 检查分类名称是否已存在
  const existingCategory = await Category.findOne({ userId, name });
  if (existingCategory) {
    throw new CustomError('分类名称已存在', 400);
  }

  const category = await Category.create({
    name,
    color,
    userId,
  });

  res.status(201).json({
    success: true,
    message: '分类创建成功',
    data: { category },
  });
}));

// 更新分类
router.put('/:id', [
  ...commonValidation.mongoId,
  ...categoryValidation.update,
], asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;
  const { name, color } = req.body;

  // 如果要更新名称，检查是否与其他分类重复
  if (name) {
    const existingCategory = await Category.findOne({
      userId,
      name,
      _id: { $ne: id }
    });
    if (existingCategory) {
      throw new CustomError('分类名称已存在', 400);
    }
  }

  const oldCategory = await Category.findOne({ _id: id, userId });
  if (!oldCategory) {
    throw new CustomError('分类不存在', 404);
  }

  const category = await Category.findOneAndUpdate(
    { _id: id, userId },
    { ...(name && { name }), ...(color && { color }) },
    { new: true, runValidators: true }
  );

  // 如果分类名称发生变化，更新相关便签的分类字段
  if (name && name !== oldCategory.name) {
    await Note.updateMany(
      { userId, category: oldCategory.name },
      { category: name }
    );
  }

  res.json({
    success: true,
    message: '分类更新成功',
    data: { category },
  });
}));

// 删除分类
router.delete('/:id', commonValidation.mongoId, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;

  const category = await Category.findOne({ _id: id, userId });

  if (!category) {
    throw new CustomError('分类不存在', 404);
  }

  // 检查是否有便签使用此分类
  const notesCount = await Note.countDocuments({ userId, category: category.name });
  
  if (notesCount > 0) {
    // 将使用此分类的便签的分类字段设为空
    await Note.updateMany(
      { userId, category: category.name },
      { $unset: { category: 1 } }
    );
  }

  await Category.findByIdAndDelete(id);

  res.json({
    success: true,
    message: '分类删除成功',
    data: { affectedNotes: notesCount },
  });
}));

// 获取分类统计信息
router.get('/:id/stats', commonValidation.mongoId, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0].msg, 400);
  }

  const userId = (req as any).user._id;
  const { id } = req.params;

  const category = await Category.findOne({ _id: id, userId });

  if (!category) {
    throw new CustomError('分类不存在', 404);
  }

  const [
    totalNotes,
    pinnedNotes,
    archivedNotes,
    recentNotes,
  ] = await Promise.all([
    Note.countDocuments({ userId, category: category.name }),
    Note.countDocuments({ userId, category: category.name, isPinned: true }),
    Note.countDocuments({ userId, category: category.name, isArchived: true }),
    Note.find({ userId, category: category.name, isArchived: false })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title createdAt')
      .lean(),
  ]);

  res.json({
    success: true,
    data: {
      category,
      stats: {
        totalNotes,
        pinnedNotes,
        archivedNotes,
        recentNotes,
      },
    },
  });
}));

module.exports = router;
