import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';
import { CustomError, asyncHandler } from './errorHandler';

export interface AuthRequest extends Request {
  user?: any;
}

export const protect = asyncHandler(async (req: AuthRequest, res: Response, next: NextFunction) => {
  let token: string | undefined;

  // 从请求头获取token
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // 检查token是否存在
  if (!token) {
    throw new CustomError('访问被拒绝，请先登录', 401);
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    // 获取用户信息
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      throw new CustomError('用户不存在', 401);
    }

    if (!user.isActive) {
      throw new CustomError('账户已被禁用', 401);
    }

    req.user = user;
    next();
  } catch (error) {
    throw new CustomError('访问令牌无效', 401);
  }
});

export const authorize = (...roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('访问被拒绝', 401);
    }

    if (!roles.includes(req.user.role)) {
      throw new CustomError('权限不足', 403);
    }

    next();
  };
};

export const optionalAuth = asyncHandler(async (req: AuthRequest, res: Response, next: NextFunction) => {
  let token: string | undefined;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      const user = await User.findById(decoded.id).select('-password');
      
      if (user && user.isActive) {
        req.user = user;
      }
    } catch (error) {
      // 可选认证，忽略错误
    }
  }

  next();
});
