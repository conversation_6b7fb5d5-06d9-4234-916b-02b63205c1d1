import { create } from 'zustand';
import { notesAPI, categoriesAPI } from '@/lib/api';

interface Note {
  _id: string;
  title: string;
  content: string;
  category?: string;
  tags: string[];
  color: string;
  isPinned: boolean;
  isArchived: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  _id: string;
  name: string;
  color: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  notesCount?: number;
}

interface NotesState {
  notes: Note[];
  categories: Category[];
  currentNote: Note | null;
  isLoading: boolean;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  filters: {
    search: string;
    category: string;
    tags: string[];
    sortBy: string;
    sortOrder: string;
    isPinned?: boolean;
    isArchived: boolean;
  };

  // Actions
  fetchNotes: () => Promise<void>;
  fetchCategories: () => Promise<void>;
  createNote: (data: Partial<Note>) => Promise<Note>;
  updateNote: (id: string, data: Partial<Note>) => Promise<Note>;
  deleteNote: (id: string) => Promise<void>;
  createCategory: (data: { name: string; color: string }) => Promise<Category>;
  updateCategory: (id: string, data: { name?: string; color?: string }) => Promise<Category>;
  deleteCategory: (id: string) => Promise<void>;
  setCurrentNote: (note: Note | null) => void;
  setFilters: (filters: Partial<NotesState['filters']>) => void;
  setLoading: (loading: boolean) => void;
  batchUpdateNotes: (ids: string[], action: string, data?: any) => Promise<void>;
}

export const useNotesStore = create<NotesState>((set, get) => ({
  notes: [],
  categories: [],
  currentNote: null,
  isLoading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  },
  filters: {
    search: '',
    category: '',
    tags: [],
    sortBy: 'createdAt',
    sortOrder: 'desc',
    isArchived: false,
  },

  fetchNotes: async () => {
    set({ isLoading: true });
    try {
      const { filters, pagination } = get();
      const response = await notesAPI.getNotes({
        ...filters,
        page: pagination.page,
        limit: pagination.limit,
      });

      const { data, pagination: paginationData } = response.data.data;
      
      set({
        notes: data,
        pagination: paginationData,
        isLoading: false,
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  fetchCategories: async () => {
    try {
      const response = await categoriesAPI.getCategories();
      set({ categories: response.data.data.categories });
    } catch (error) {
      throw error;
    }
  },

  createNote: async (data: Partial<Note>) => {
    try {
      const response = await notesAPI.createNote(data as any);
      const newNote = response.data.data.note;
      
      set((state) => ({
        notes: [newNote, ...state.notes],
      }));
      
      return newNote;
    } catch (error) {
      throw error;
    }
  },

  updateNote: async (id: string, data: Partial<Note>) => {
    try {
      const response = await notesAPI.updateNote(id, data);
      const updatedNote = response.data.data.note;
      
      set((state) => ({
        notes: state.notes.map((note) =>
          note._id === id ? updatedNote : note
        ),
        currentNote: state.currentNote?._id === id ? updatedNote : state.currentNote,
      }));
      
      return updatedNote;
    } catch (error) {
      throw error;
    }
  },

  deleteNote: async (id: string) => {
    try {
      await notesAPI.deleteNote(id);
      
      set((state) => ({
        notes: state.notes.filter((note) => note._id !== id),
        currentNote: state.currentNote?._id === id ? null : state.currentNote,
      }));
    } catch (error) {
      throw error;
    }
  },

  createCategory: async (data: { name: string; color: string }) => {
    try {
      const response = await categoriesAPI.createCategory(data);
      const newCategory = response.data.data.category;
      
      set((state) => ({
        categories: [...state.categories, newCategory],
      }));
      
      return newCategory;
    } catch (error) {
      throw error;
    }
  },

  updateCategory: async (id: string, data: { name?: string; color?: string }) => {
    try {
      const response = await categoriesAPI.updateCategory(id, data);
      const updatedCategory = response.data.data.category;
      
      set((state) => ({
        categories: state.categories.map((category) =>
          category._id === id ? updatedCategory : category
        ),
      }));
      
      return updatedCategory;
    } catch (error) {
      throw error;
    }
  },

  deleteCategory: async (id: string) => {
    try {
      await categoriesAPI.deleteCategory(id);
      
      set((state) => ({
        categories: state.categories.filter((category) => category._id !== id),
      }));
    } catch (error) {
      throw error;
    }
  },

  batchUpdateNotes: async (ids: string[], action: string, data?: any) => {
    try {
      await notesAPI.batchUpdate({ ids, action, data });
      
      // 重新获取便签列表
      await get().fetchNotes();
    } catch (error) {
      throw error;
    }
  },

  setCurrentNote: (note: Note | null) => {
    set({ currentNote: note });
  },

  setFilters: (filters: Partial<NotesState['filters']>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
      pagination: { ...state.pagination, page: 1 }, // 重置页码
    }));
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
