import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // 处理401错误（未授权）
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api'}/auth/refresh`, {
            refreshToken,
          });

          const { accessToken, refreshToken: newRefreshToken } = response.data.data;
          localStorage.setItem('token', accessToken);
          localStorage.setItem('refreshToken', newRefreshToken);

          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // 刷新失败，清除本地存储并跳转到登录页
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // 显示错误消息
    const message = error.response?.data?.error || error.message || '请求失败';
    toast.error(message);

    return Promise.reject(error);
  }
);

// API接口类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// 认证相关API
export const authAPI = {
  register: (data: { name: string; email: string; password: string }) =>
    api.post<ApiResponse>('/auth/register', data),

  login: (data: { email: string; password: string }) =>
    api.post<ApiResponse>('/auth/login', data),

  refresh: (refreshToken: string) =>
    api.post<ApiResponse>('/auth/refresh', { refreshToken }),

  getProfile: () =>
    api.get<ApiResponse>('/auth/me'),

  updateProfile: (data: { username?: string; avatar?: string }) =>
    api.put<ApiResponse>('/auth/profile', data),

  changePassword: (data: { currentPassword: string; newPassword: string }) =>
    api.put<ApiResponse>('/auth/password', data),
};

// 便签相关API
export const notesAPI = {
  getNotes: (params?: {
    page?: number;
    limit?: number;
    category?: string;
    tags?: string[];
    search?: string;
    sortBy?: string;
    sortOrder?: string;
    isPinned?: boolean;
    isArchived?: boolean;
  }) => api.get<ApiResponse<PaginatedResponse<any>>>('/notes', { params }),

  getNote: (id: string) =>
    api.get<ApiResponse>(`/notes/${id}`),

  createNote: (data: {
    title: string;
    content: string;
    category?: string;
    tags?: string[];
    color?: string;
    isPinned?: boolean;
  }) => api.post<ApiResponse>('/notes', data),

  updateNote: (id: string, data: any) =>
    api.put<ApiResponse>(`/notes/${id}`, data),

  deleteNote: (id: string) =>
    api.delete<ApiResponse>(`/notes/${id}`),

  batchUpdate: (data: {
    ids: string[];
    action: string;
    data?: any;
  }) => api.patch<ApiResponse>('/notes/batch', data),

  getStats: () =>
    api.get<ApiResponse>('/notes/stats/overview'),
};

// 分类相关API
export const categoriesAPI = {
  getCategories: () =>
    api.get<ApiResponse>('/categories'),

  getCategory: (id: string) =>
    api.get<ApiResponse>(`/categories/${id}`),

  createCategory: (data: { name: string; color: string }) =>
    api.post<ApiResponse>('/categories', data),

  updateCategory: (id: string, data: { name?: string; color?: string }) =>
    api.put<ApiResponse>(`/categories/${id}`, data),

  deleteCategory: (id: string) =>
    api.delete<ApiResponse>(`/categories/${id}`),

  getCategoryStats: (id: string) =>
    api.get<ApiResponse>(`/categories/${id}/stats`),
};

// 搜索相关API
export const searchAPI = {
  search: (params: {
    q: string;
    type?: string;
    page?: number;
    limit?: number;
  }) => api.get<ApiResponse>('/search', { params }),

  getSuggestions: (q: string) =>
    api.get<ApiResponse>('/search/suggestions', { params: { q } }),

  getPopularTags: (limit?: number) =>
    api.get<ApiResponse>('/search/tags/popular', { params: { limit } }),

  getAllTags: () =>
    api.get<ApiResponse>('/search/tags'),

  renameTag: (tagName: string, newName: string) =>
    api.put<ApiResponse>(`/search/tags/${encodeURIComponent(tagName)}`, { newName }),

  deleteTag: (tagName: string) =>
    api.delete<ApiResponse>(`/search/tags/${encodeURIComponent(tagName)}`),
};

// 导入导出相关API
export const exportAPI = {
  exportNotes: (params: {
    format?: string;
    includeArchived?: boolean;
  }) => api.get('/export/notes', {
    params,
    responseType: 'blob' // 用于文件下载
  }),

  importNotes: (data: {
    data: any;
    options?: {
      skipDuplicates?: boolean;
      updateExisting?: boolean;
      importCategories?: boolean;
    };
  }) => api.post<ApiResponse>('/export/notes', data),

  getTemplate: () =>
    api.get<ApiResponse>('/export/template'),
};

// 管理员相关API
export const adminAPI = {
  getStats: () =>
    api.get<ApiResponse>('/admin/stats'),

  getUsers: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: string;
  }) => api.get<ApiResponse<PaginatedResponse<any>>>('/admin/users', { params }),

  getUser: (id: string) =>
    api.get<ApiResponse>(`/admin/users/${id}`),

  updateUserStatus: (id: string, isActive: boolean) =>
    api.patch<ApiResponse>(`/admin/users/${id}/status`, { isActive }),

  updateUserRole: (id: string, role: string) =>
    api.patch<ApiResponse>(`/admin/users/${id}/role`, { role }),

  deleteUser: (id: string) =>
    api.delete<ApiResponse>(`/admin/users/${id}`),

  getNotes: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    userId?: string;
    sortBy?: string;
    sortOrder?: string;
  }) => api.get<ApiResponse<PaginatedResponse<any>>>('/admin/notes', { params }),

  deleteNote: (id: string) =>
    api.delete<ApiResponse>(`/admin/notes/${id}`),

  getConfig: () =>
    api.get<ApiResponse>('/admin/config'),
};

export default api;
