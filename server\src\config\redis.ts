import { createClient, RedisClientType } from 'redis';
import dotenv from 'dotenv';

dotenv.config();

class RedisConfig {
  private client: RedisClientType | null = null;
  private isConnected: boolean = false;

  constructor() {
    this.initializeClient();
  }

  private initializeClient() {
    const redisUrl = process.env.REDIS_URL || 
      `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || 6379}`;

    this.client = createClient({
      url: redisUrl,
      password: process.env.REDIS_PASSWORD || undefined,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 1000),
      },
    });

    // 错误处理
    this.client.on('error', (err) => {
      console.error('❌ Redis 连接错误:', err);
      this.isConnected = false;
    });

    this.client.on('connect', () => {
      console.log('🔄 Redis 正在连接...');
    });

    this.client.on('ready', () => {
      console.log('✅ Redis 连接成功');
      this.isConnected = true;
    });

    this.client.on('end', () => {
      console.log('🔌 Redis 连接已关闭');
      this.isConnected = false;
    });
  }

  async connect(): Promise<void> {
    if (!this.client) {
      throw new Error('Redis 客户端未初始化');
    }

    try {
      await this.client.connect();
    } catch (error) {
      console.error('❌ Redis 连接失败:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      try {
        await this.client.quit();
        console.log('🔌 Redis 连接已关闭');
      } catch (error) {
        console.error('❌ 关闭 Redis 连接时出错:', error);
      }
    }
  }

  getClient(): RedisClientType {
    if (!this.client) {
      throw new Error('Redis 客户端未初始化');
    }
    return this.client;
  }

  isReady(): boolean {
    return this.isConnected && this.client?.isReady === true;
  }

  // 缓存操作方法
  async set(key: string, value: any, expireInSeconds?: number): Promise<void> {
    if (!this.isReady()) {
      console.warn('⚠️ Redis 未连接，跳过缓存设置');
      return;
    }

    try {
      const serializedValue = JSON.stringify(value);
      if (expireInSeconds) {
        await this.client!.setEx(key, expireInSeconds, serializedValue);
      } else {
        await this.client!.set(key, serializedValue);
      }
    } catch (error) {
      console.error('❌ Redis 设置缓存失败:', error);
    }
  }

  async get(key: string): Promise<any | null> {
    if (!this.isReady()) {
      console.warn('⚠️ Redis 未连接，跳过缓存获取');
      return null;
    }

    try {
      const value = await this.client!.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('❌ Redis 获取缓存失败:', error);
      return null;
    }
  }

  async del(key: string): Promise<void> {
    if (!this.isReady()) {
      console.warn('⚠️ Redis 未连接，跳过缓存删除');
      return;
    }

    try {
      await this.client!.del(key);
    } catch (error) {
      console.error('❌ Redis 删除缓存失败:', error);
    }
  }

  async exists(key: string): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      console.error('❌ Redis 检查键存在失败:', error);
      return false;
    }
  }

  async expire(key: string, seconds: number): Promise<void> {
    if (!this.isReady()) {
      return;
    }

    try {
      await this.client!.expire(key, seconds);
    } catch (error) {
      console.error('❌ Redis 设置过期时间失败:', error);
    }
  }

  // 批量操作
  async mget(keys: string[]): Promise<(any | null)[]> {
    if (!this.isReady() || keys.length === 0) {
      return [];
    }

    try {
      const values = await this.client!.mGet(keys);
      return values.map(value => value ? JSON.parse(value) : null);
    } catch (error) {
      console.error('❌ Redis 批量获取失败:', error);
      return [];
    }
  }

  async mset(keyValuePairs: Record<string, any>): Promise<void> {
    if (!this.isReady()) {
      return;
    }

    try {
      const serializedPairs: Record<string, string> = {};
      Object.entries(keyValuePairs).forEach(([key, value]) => {
        serializedPairs[key] = JSON.stringify(value);
      });
      await this.client!.mSet(serializedPairs);
    } catch (error) {
      console.error('❌ Redis 批量设置失败:', error);
    }
  }

  // 清除匹配模式的键
  async deletePattern(pattern: string): Promise<number> {
    if (!this.isReady()) {
      return 0;
    }

    try {
      const keys = await this.client!.keys(pattern);
      if (keys.length > 0) {
        await this.client!.del(keys);
        return keys.length;
      }
      return 0;
    } catch (error) {
      console.error('❌ Redis 删除模式匹配键失败:', error);
      return 0;
    }
  }
}

export const redisConfig = new RedisConfig();
