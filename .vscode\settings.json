{"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true}, "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "editor.quickSuggestions": {"strings": true}, "css.validate": false, "less.validate": false, "scss.validate": false}