import React, { useState, useEffect } from 'react';
import { Header } from '@/components/Layout/Header';
import { Sidebar } from '@/components/Layout/Sidebar';
import { NotesGrid } from '@/components/Notes/NotesGrid';
import { NoteEditor } from '@/components/Notes/NoteEditor';
import { AdminPanel } from '@/components/Admin/AdminPanel';
import { useNotesStore } from '@/store/notesStore';
import { useAuthStore } from '@/store/authStore';
import { toast } from 'react-hot-toast';

type ViewMode = 'notes' | 'admin' | 'settings';

export const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    notes, 
    fetchNotes, 
    fetchCategories, 
    updateNote, 
    deleteNote, 
    isLoading 
  } = useNotesStore();
  
  const [currentView, setCurrentView] = useState<ViewMode>('notes');
  const [selectedNote, setSelectedNote] = useState<any>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    // 初始化数据
    fetchNotes();
    fetchCategories();
  }, [fetchNotes, fetchCategories]);

  const handleCreateNote = () => {
    setSelectedNote(null);
    setIsEditorOpen(true);
  };

  const handleEditNote = (note: any) => {
    setSelectedNote(note);
    setIsEditorOpen(true);
  };

  const handleDeleteNote = async (id: string) => {
    if (window.confirm('确定要删除这个便签吗？')) {
      try {
        await deleteNote(id);
        toast.success('便签删除成功');
      } catch (error) {
        toast.error('删除失败，请重试');
      }
    }
  };

  const handlePinNote = async (id: string, isPinned: boolean) => {
    try {
      await updateNote(id, { isPinned });
      toast.success(isPinned ? '便签已置顶' : '已取消置顶');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleArchiveNote = async (id: string, isArchived: boolean) => {
    try {
      await updateNote(id, { isArchived });
      toast.success(isArchived ? '便签已归档' : '已取消归档');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleCloseEditor = () => {
    setIsEditorOpen(false);
    setSelectedNote(null);
    // 重新获取便签列表以显示最新数据
    fetchNotes();
  };

  const handleOpenSettings = () => {
    setCurrentView('settings');
  };

  const renderMainContent = () => {
    switch (currentView) {
      case 'admin':
        return user?.role === 'admin' ? (
          <AdminPanel />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">访问被拒绝</h2>
              <p className="text-gray-600">您没有管理员权限</p>
            </div>
          </div>
        );
      
      case 'settings':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-6">设置</h1>
            <div className="max-w-2xl">
              <div className="bg-white rounded-lg border p-6">
                <h2 className="text-lg font-semibold mb-4">个人信息</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用户名
                    </label>
                    <p className="text-gray-900">{user?.username}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      邮箱
                    </label>
                    <p className="text-gray-900">{user?.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      角色
                    </label>
                    <p className="text-gray-900">
                      {user?.role === 'admin' ? '管理员' : '普通用户'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="flex-1 overflow-hidden">
            <NotesGrid
              notes={notes}
              isLoading={isLoading}
              onEdit={handleEditNote}
              onDelete={handleDeleteNote}
              onPin={handlePinNote}
              onArchive={handleArchiveNote}
            />
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex flex-col">
      {/* 顶部导航 */}
      <Header 
        onCreateNote={handleCreateNote}
        onOpenSettings={handleOpenSettings}
      />

      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar
          currentView={currentView}
          onViewChange={setCurrentView}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          showAdminPanel={user?.role === 'admin'}
        />

        {/* 主内容区域 */}
        <main className="flex-1 overflow-hidden bg-gray-50">
          {renderMainContent()}
        </main>
      </div>

      {/* 便签编辑器 */}
      <NoteEditor
        note={selectedNote}
        isOpen={isEditorOpen}
        onClose={handleCloseEditor}
      />
    </div>
  );
};
