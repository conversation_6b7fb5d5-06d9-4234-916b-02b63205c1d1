import mongoose, { Document, Schema } from 'mongoose';

export interface ICategory extends Document {
  name: string;
  color: string;
  userId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const categorySchema = new Schema<ICategory>({
  name: {
    type: String,
    required: [true, '分类名称不能为空'],
    trim: true,
    maxlength: [50, '分类名称不能超过50个字符'],
  },
  color: {
    type: String,
    required: [true, '分类颜色不能为空'],
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, '请输入有效的颜色值'],
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true,
});

// 复合索引：确保同一用户下分类名称唯一
categorySchema.index({ userId: 1, name: 1 }, { unique: true });

// 虚拟字段：该分类下的便签数量
categorySchema.virtual('notesCount', {
  ref: 'Note',
  localField: 'name',
  foreignField: 'category',
  count: true,
  match: function() {
    return { userId: this.userId };
  },
});

// 确保虚拟字段被序列化
categorySchema.set('toJSON', { virtuals: true });
categorySchema.set('toObject', { virtuals: true });

export const Category = mongoose.model<ICategory>('Category', categorySchema);
