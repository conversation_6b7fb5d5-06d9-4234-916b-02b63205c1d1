import { NextFunction, Request, Response } from 'express';
import { redisConfig } from '../config/redis';

interface PerformanceMetrics {
  endpoint: string;
  method: string;
  responseTime: number;
  statusCode: number;
  timestamp: Date;
  userId?: string;
  userAgent?: string;
  ip: string;
}

/**
 * 性能监控中间件
 */
export const performanceMonitor = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const originalSend = res.send;

  // 拦截响应
  res.send = function (data: any) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // 收集性能指标
    const userAgent = req.get('User-Agent');
    const ip = req.ip || (req as any).connection?.remoteAddress || 'unknown';

    const metrics: PerformanceMetrics = {
      endpoint: req.path,
      method: req.method,
      responseTime,
      statusCode: res.statusCode,
      timestamp: new Date(),
      userId: (req as any).user?._id,
      ip,
    };

    if (userAgent) {
      metrics.userAgent = userAgent;
    }

    // 异步记录性能指标
    setImmediate(() => {
      recordPerformanceMetrics(metrics);
    });

    // 在响应头中添加响应时间
    res.set('X-Response-Time', `${responseTime}ms`);

    // 调用原始的 send 方法
    return originalSend.call(this, data);
  };

  next();
};

/**
 * 记录性能指标
 */
async function recordPerformanceMetrics(metrics: PerformanceMetrics): Promise<void> {
  try {
    // 记录到Redis（用于实时监控）
    const key = `metrics:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    await redisConfig.set(key, metrics, 3600); // 保存1小时

    // 记录慢查询（响应时间超过1秒）
    if (metrics.responseTime > 1000) {
      const slowQueryKey = `slow_queries:${new Date().toISOString().split('T')[0]}`;
      const slowQueries = await redisConfig.get(slowQueryKey) || [];
      slowQueries.push(metrics);
      await redisConfig.set(slowQueryKey, slowQueries, 86400); // 保存24小时

      console.warn(`🐌 慢查询检测: ${metrics.method} ${metrics.endpoint} - ${metrics.responseTime}ms`);
    }

    // 记录错误请求（4xx, 5xx状态码）
    if (metrics.statusCode >= 400) {
      const errorKey = `errors:${new Date().toISOString().split('T')[0]}`;
      const errors = await redisConfig.get(errorKey) || [];
      errors.push(metrics);
      await redisConfig.set(errorKey, errors, 86400); // 保存24小时
    }

    // 更新端点统计
    await updateEndpointStats(metrics);

  } catch (error) {
    console.error('❌ 记录性能指标失败:', error);
  }
}

/**
 * 更新端点统计信息
 */
async function updateEndpointStats(metrics: PerformanceMetrics): Promise<void> {
  try {
    const statsKey = `endpoint_stats:${metrics.method}:${metrics.endpoint}`;
    const stats = await redisConfig.get(statsKey) || {
      totalRequests: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      errorCount: 0,
      lastAccessed: null,
    };

    // 更新统计信息
    stats.totalRequests += 1;
    stats.totalResponseTime += metrics.responseTime;
    stats.averageResponseTime = stats.totalResponseTime / stats.totalRequests;
    stats.minResponseTime = Math.min(stats.minResponseTime, metrics.responseTime);
    stats.maxResponseTime = Math.max(stats.maxResponseTime, metrics.responseTime);
    stats.lastAccessed = metrics.timestamp;

    if (metrics.statusCode >= 400) {
      stats.errorCount += 1;
    }

    // 保存更新后的统计信息
    await redisConfig.set(statsKey, stats, 86400 * 7); // 保存7天

  } catch (error) {
    console.error('❌ 更新端点统计失败:', error);
  }
}

/**
 * 获取性能统计信息
 */
export async function getPerformanceStats(): Promise<any> {
  try {
    const today = new Date().toISOString().split('T')[0];

    // 获取今日慢查询
    const slowQueries = await redisConfig.get(`slow_queries:${today}`) || [];

    // 获取今日错误
    const errors = await redisConfig.get(`errors:${today}`) || [];

    // 获取端点统计（这里简化处理，实际应用中可能需要更复杂的查询）
    const endpointStatsKeys = await redisConfig.getClient().keys('endpoint_stats:*');
    const endpointStats: any[] = [];

    for (const key of endpointStatsKeys.slice(0, 20)) { // 限制返回数量
      const stats = await redisConfig.get(key);
      if (stats) {
        const [, method, ...pathParts] = key.split(':');
        endpointStats.push({
          method,
          endpoint: pathParts.join(':'),
          ...stats,
        });
      }
    }

    // 按平均响应时间排序
    endpointStats.sort((a, b) => b.averageResponseTime - a.averageResponseTime);

    return {
      slowQueries: slowQueries.slice(-10), // 最近10个慢查询
      errors: errors.slice(-10), // 最近10个错误
      topSlowEndpoints: endpointStats.slice(0, 10), // 最慢的10个端点
      summary: {
        totalSlowQueries: slowQueries.length,
        totalErrors: errors.length,
        totalEndpoints: endpointStats.length,
      },
    };

  } catch (error) {
    console.error('❌ 获取性能统计失败:', error);
    return {
      slowQueries: [],
      errors: [],
      topSlowEndpoints: [],
      summary: {
        totalSlowQueries: 0,
        totalErrors: 0,
        totalEndpoints: 0,
      },
    };
  }
}

/**
 * 清理过期的性能数据
 */
export async function cleanupPerformanceData(): Promise<void> {
  try {
    // 清理超过7天的性能指标
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    const deletedMetrics = await redisConfig.deletePattern(`metrics:${sevenDaysAgo}*`);

    // 清理超过30天的慢查询和错误记录
    const thirtyDaysAgo = new Date(Date.now() - (30 * 24 * 60 * 60 * 1000));
    const oldDateKey = thirtyDaysAgo.toISOString().split('T')[0];

    await redisConfig.del(`slow_queries:${oldDateKey}`);
    await redisConfig.del(`errors:${oldDateKey}`);

    console.log(`🧹 性能数据清理完成: 删除了 ${deletedMetrics} 个指标记录`);

  } catch (error) {
    console.error('❌ 清理性能数据失败:', error);
  }
}

/**
 * 实时性能监控报警
 */
export function setupPerformanceAlerts(): void {
  setInterval(async () => {
    try {
      const stats = await getPerformanceStats();

      // 检查是否有过多的慢查询
      if (stats.summary.totalSlowQueries > 50) {
        console.warn(`⚠️ 性能警告: 今日慢查询数量过多 (${stats.summary.totalSlowQueries})`);
      }

      // 检查是否有过多的错误
      if (stats.summary.totalErrors > 100) {
        console.warn(`⚠️ 错误警告: 今日错误数量过多 (${stats.summary.totalErrors})`);
      }

    } catch (error) {
      console.error('❌ 性能监控检查失败:', error);
    }
  }, 5 * 60 * 1000); // 每5分钟检查一次

  console.log('📊 性能监控报警已启动');
}
