import express from 'express';
import { protect, authorize } from '../middleware/auth';
import { getPerformanceStats, cleanupPerformanceData } from '../middleware/performance';
import { redisConfig } from '../config/redis';
import { asyncHandler } from '../middleware/errorHandler';
import os from 'os';

const router = express.Router();

// 所有监控路由都需要管理员权限
router.use(protect);
router.use(authorize('admin'));

// 获取系统健康状态
router.get('/health', asyncHandler(async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
    system: {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      loadAverage: os.loadavg(),
    },
    services: {
      database: 'connected', // 这里可以添加实际的数据库连接检查
      redis: redisConfig.isReady() ? 'connected' : 'disconnected',
    },
    memory: {
      used: process.memoryUsage(),
      heap: {
        used: process.memoryUsage().heapUsed,
        total: process.memoryUsage().heapTotal,
        external: process.memoryUsage().external,
      },
    },
  };

  res.json({
    success: true,
    data: health,
  });
}));

// 获取性能统计
router.get('/performance', asyncHandler(async (req, res) => {
  const stats = await getPerformanceStats();
  
  res.json({
    success: true,
    data: stats,
  });
}));

// 获取实时指标
router.get('/metrics', asyncHandler(async (req, res) => {
  const { timeRange = '1h' } = req.query;
  
  try {
    // 根据时间范围获取指标
    let startTime: number;
    switch (timeRange) {
      case '5m':
        startTime = Date.now() - (5 * 60 * 1000);
        break;
      case '1h':
        startTime = Date.now() - (60 * 60 * 1000);
        break;
      case '24h':
        startTime = Date.now() - (24 * 60 * 60 * 1000);
        break;
      default:
        startTime = Date.now() - (60 * 60 * 1000);
    }

    // 获取指标数据（这里简化处理）
    const metricsKeys = await redisConfig.getClient().keys(`metrics:${startTime}*`);
    const metrics = [];
    
    for (const key of metricsKeys.slice(0, 100)) { // 限制数量
      const metric = await redisConfig.get(key);
      if (metric) {
        metrics.push(metric);
      }
    }

    // 计算聚合数据
    const aggregated = {
      totalRequests: metrics.length,
      averageResponseTime: metrics.length > 0 
        ? metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length 
        : 0,
      errorRate: metrics.length > 0 
        ? (metrics.filter(m => m.statusCode >= 400).length / metrics.length) * 100 
        : 0,
      requestsPerMinute: metrics.length > 0 
        ? (metrics.length / ((Date.now() - startTime) / 60000)) 
        : 0,
      statusCodes: metrics.reduce((acc, m) => {
        acc[m.statusCode] = (acc[m.statusCode] || 0) + 1;
        return acc;
      }, {} as Record<number, number>),
    };

    res.json({
      success: true,
      data: {
        timeRange,
        aggregated,
        rawMetrics: metrics.slice(-20), // 最近20个原始指标
      },
    });

  } catch (error) {
    res.json({
      success: true,
      data: {
        timeRange,
        aggregated: {
          totalRequests: 0,
          averageResponseTime: 0,
          errorRate: 0,
          requestsPerMinute: 0,
          statusCodes: {},
        },
        rawMetrics: [],
        error: 'Redis not available',
      },
    });
  }
}));

// 获取缓存统计
router.get('/cache', asyncHandler(async (req, res) => {
  try {
    const info = await redisConfig.getClient().info('memory');
    const keyspace = await redisConfig.getClient().info('keyspace');
    
    // 获取缓存命中率等统计信息
    const cacheStats = {
      connected: redisConfig.isReady(),
      memory: info,
      keyspace: keyspace,
      // 这里可以添加更多缓存相关的统计信息
    };

    res.json({
      success: true,
      data: cacheStats,
    });

  } catch (error) {
    res.json({
      success: true,
      data: {
        connected: false,
        error: 'Redis not available',
      },
    });
  }
}));

// 清理性能数据
router.post('/cleanup', asyncHandler(async (req, res) => {
  await cleanupPerformanceData();
  
  res.json({
    success: true,
    message: '性能数据清理完成',
  });
}));

// 获取错误日志
router.get('/errors', asyncHandler(async (req, res) => {
  const { date = new Date().toISOString().split('T')[0] } = req.query;
  
  try {
    const errors = await redisConfig.get(`errors:${date}`) || [];
    
    res.json({
      success: true,
      data: {
        date,
        errors: errors.slice(-50), // 最近50个错误
        total: errors.length,
      },
    });

  } catch (error) {
    res.json({
      success: true,
      data: {
        date,
        errors: [],
        total: 0,
        error: 'Redis not available',
      },
    });
  }
}));

// 获取慢查询日志
router.get('/slow-queries', asyncHandler(async (req, res) => {
  const { date = new Date().toISOString().split('T')[0] } = req.query;
  
  try {
    const slowQueries = await redisConfig.get(`slow_queries:${date}`) || [];
    
    res.json({
      success: true,
      data: {
        date,
        slowQueries: slowQueries.slice(-50), // 最近50个慢查询
        total: slowQueries.length,
      },
    });

  } catch (error) {
    res.json({
      success: true,
      data: {
        date,
        slowQueries: [],
        total: 0,
        error: 'Redis not available',
      },
    });
  }
}));

// 获取API端点统计
router.get('/endpoints', asyncHandler(async (req, res) => {
  try {
    const endpointKeys = await redisConfig.getClient().keys('endpoint_stats:*');
    const endpoints = [];
    
    for (const key of endpointKeys) {
      const stats = await redisConfig.get(key);
      if (stats) {
        const [, method, ...pathParts] = key.split(':');
        endpoints.push({
          method,
          endpoint: pathParts.join(':'),
          ...stats,
        });
      }
    }

    // 按请求数量排序
    endpoints.sort((a, b) => b.totalRequests - a.totalRequests);

    res.json({
      success: true,
      data: {
        endpoints: endpoints.slice(0, 50), // 前50个端点
        total: endpoints.length,
      },
    });

  } catch (error) {
    res.json({
      success: true,
      data: {
        endpoints: [],
        total: 0,
        error: 'Redis not available',
      },
    });
  }
}));

module.exports = router;
